#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update all agents to use the verbosity manager
"""

import os
import re
from pathlib import Path

def update_agent_file(file_path: str, agent_name: str):
    """Update a single agent file to use verbosity manager"""
    
    print(f"Updating {file_path}...")
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check if already updated
        if 'from manage_verbosity import' in content:
            print(f"   {file_path} already updated")
            return True
        
        # Add import
        import_pattern = r'(from dotenv import load_dotenv)'
        import_replacement = r'\1\nfrom manage_verbosity import get_agent_verbose, get_agent_config'
        
        if 'from dotenv import load_dotenv' in content:
            content = re.sub(import_pattern, import_replacement, content)
        else:
            # Add import after other imports
            import_lines = []
            other_lines = []
            in_imports = True
            
            for line in content.split('\n'):
                if line.startswith('import ') or line.startswith('from ') or line.strip() == '':
                    if in_imports:
                        import_lines.append(line)
                    else:
                        other_lines.append(line)
                else:
                    if in_imports:
                        import_lines.append('from manage_verbosity import get_agent_verbose, get_agent_config')
                        in_imports = False
                    other_lines.append(line)
            
            content = '\n'.join(import_lines + other_lines)
        
        # Update verbose=True to use configuration
        verbose_pattern = r'verbose=True'
        verbose_replacement = f'''# Get verbosity setting from configuration
        agent_config = get_agent_config("{agent_name}")
        verbose = agent_config.get("verbose", False)
        allow_delegation = agent_config.get("allow_delegation", False)
        
        return Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            verbose=verbose,
            allow_delegation=allow_delegation'''
        
        # This is a simplified replacement - in practice, each agent would need manual updating
        # due to different structures
        
        print(f"  ⚠️  {file_path} needs manual update")
        return False
        
    except Exception as e:
        print(f"   Error updating {file_path}: {e}")
        return False

def main():
    """Update all agent files"""
    
    print("🔧 Updating All Agents to Use Verbosity Manager")
    print("=" * 60)
    
    # Agent files to update
    agents = [
        ("agents/order_validation_agent.py", "order_validation_agent"),
        ("agents/credit_management_agent.py", "credit_management_agent"),
        ("agents/inventory_management_agent.py", "inventory_management_agent"),
        ("agents/pricing_management_agent.py", "pricing_management_agent"),
        ("agents/fulfillment_coordination_agent.py", "fulfillment_coordination_agent"),
        ("agents/invoice_generation_agent.py", "invoice_generation_agent"),
    ]
    
    updated_count = 0
    total_count = len(agents)
    
    for file_path, agent_name in agents:
        if os.path.exists(file_path):
            if update_agent_file(file_path, agent_name):
                updated_count += 1
        else:
            print(f"   File not found: {file_path}")
    
    print(f"\n📊 Summary:")
    print(f"   Total agents: {total_count}")
    print(f"   Updated: {updated_count}")
    print(f"   Manual update needed: {total_count - updated_count}")
    
    print(f"\n Agents already updated:")
    print(f"   - PO Parser Agent (agents/po_parser_agent.py)")
    print(f"   - O2C Crew (o2c_crew.py)")
    
    print(f"\n To manually update remaining agents:")
    print(f"   1. Add import: from manage_verbosity import get_agent_config")
    print(f"   2. Replace verbose=True with:")
    print(f"      agent_config = get_agent_config('agent_name')")
    print(f"      verbose = agent_config.get('verbose', False)")
    print(f"   3. Use verbose=verbose in Agent constructor")

if __name__ == "__main__":
    main()
