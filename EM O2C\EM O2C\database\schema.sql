-- O2C Master Data SQLite Schema
-- Created: 2025-07-09
-- Purpose: Master data tables for Order-to-Cash system

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- =====================================================
-- CUSTOMERS TABLE
-- =====================================================
CREATE TABLE customers (
    customer_id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED')),
    credit_limit REAL NOT NULL DEFAULT 0.0,
    payment_terms TEXT NOT NULL DEFAULT 'Net 30',
    risk_score TEXT NOT NULL CHECK (risk_score IN ('LOW', 'MEDIUM', 'HIGH')),
    tax_exempt BOOLEAN NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now')),
    
    -- Contact Information
    email TEXT,
    phone TEXT,
    
    -- Financial Information
    annual_revenue REAL,
    employees INTEGER,
    duns_number TEXT,
    
    -- Credit Information
    credit_score INTEGER,
    credit_rating TEXT,
    established_date TEXT,
    industry TEXT,
    current_balance REAL DEFAULT 0.0,
    available_credit REAL,
    last_credit_review TEXT,
    financial_strength TEXT,
    industry_risk TEXT,
    geographic_risk TEXT,
    concentration_risk TEXT
);

-- =====================================================
-- CUSTOMER ADDRESSES TABLE
-- =====================================================
CREATE TABLE customer_addresses (
    address_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id TEXT NOT NULL,
    address_type TEXT NOT NULL CHECK (address_type IN ('BILLING', 'SHIPPING', 'BOTH')),
    street TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'USA',
    is_primary BOOLEAN NOT NULL DEFAULT 0,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);

-- =====================================================
-- PRODUCTS TABLE
-- =====================================================
CREATE TABLE products (
    product_code TEXT PRIMARY KEY,
    description TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'INACTIVE', 'DISCONTINUED')),
    unit_price REAL NOT NULL,
    category TEXT NOT NULL,
    tax_code TEXT NOT NULL,
    minimum_order INTEGER NOT NULL DEFAULT 1,
    maximum_discount REAL NOT NULL DEFAULT 0.0,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now')),
    
    -- Additional product attributes
    weight REAL,
    dimensions TEXT,
    manufacturer TEXT,
    supplier_code TEXT,
    lead_time_days INTEGER DEFAULT 0
);

-- =====================================================
-- BUSINESS RULES TABLE
-- =====================================================
CREATE TABLE business_rules (
    rule_id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL UNIQUE,
    rule_category TEXT NOT NULL,
    rule_value TEXT NOT NULL,
    rule_type TEXT NOT NULL CHECK (rule_type IN ('NUMERIC', 'TEXT', 'BOOLEAN', 'JSON')),
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now'))
);

-- =====================================================
-- TAX RATES TABLE
-- =====================================================
CREATE TABLE tax_rates (
    tax_id INTEGER PRIMARY KEY AUTOINCREMENT,
    state_code TEXT NOT NULL,
    tax_rate REAL NOT NULL,
    effective_date TEXT NOT NULL,
    end_date TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_date TEXT NOT NULL DEFAULT (datetime('now'))
);

-- =====================================================
-- PAYMENT HISTORY TABLE
-- =====================================================
CREATE TABLE payment_history (
    payment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id TEXT NOT NULL,
    order_number TEXT,
    payment_date TEXT NOT NULL,
    amount REAL NOT NULL,
    days_to_pay INTEGER,
    payment_method TEXT,
    status TEXT NOT NULL CHECK (status IN ('ON_TIME', 'LATE', 'DISPUTED', 'PARTIAL')),
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Customer indexes
CREATE INDEX idx_customers_name ON customers(name);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_risk_score ON customers(risk_score);

-- Address indexes
CREATE INDEX idx_addresses_customer ON customer_addresses(customer_id);
CREATE INDEX idx_addresses_type ON customer_addresses(address_type);

-- Product indexes
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_status ON products(status);

-- Business rules indexes
CREATE INDEX idx_rules_category ON business_rules(rule_category);
CREATE INDEX idx_rules_active ON business_rules(is_active);

-- Tax rates indexes
CREATE INDEX idx_tax_state ON tax_rates(state_code);
CREATE INDEX idx_tax_active ON tax_rates(is_active);

-- Payment history indexes
CREATE INDEX idx_payment_customer ON payment_history(customer_id);
CREATE INDEX idx_payment_date ON payment_history(payment_date);

-- =====================================================
-- TRIGGERS FOR UPDATED_DATE
-- =====================================================

-- Update customers updated_date on changes
CREATE TRIGGER update_customers_timestamp 
    AFTER UPDATE ON customers
    FOR EACH ROW
BEGIN
    UPDATE customers SET updated_date = datetime('now') WHERE customer_id = NEW.customer_id;
END;

-- Update products updated_date on changes
CREATE TRIGGER update_products_timestamp 
    AFTER UPDATE ON products
    FOR EACH ROW
BEGIN
    UPDATE products SET updated_date = datetime('now') WHERE product_code = NEW.product_code;
END;

-- Update business_rules updated_date on changes
CREATE TRIGGER update_business_rules_timestamp
    AFTER UPDATE ON business_rules
    FOR EACH ROW
BEGIN
    UPDATE business_rules SET updated_date = datetime('now') WHERE rule_id = NEW.rule_id;
END;

-- =====================================================
-- INVENTORY MANAGEMENT TABLES
-- =====================================================

-- =====================================================
-- INVENTORY LEVELS TABLE
-- =====================================================
CREATE TABLE inventory_levels (
    inventory_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT NOT NULL,
    warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
    quantity_on_hand INTEGER NOT NULL DEFAULT 0,
    quantity_available INTEGER NOT NULL DEFAULT 0,
    quantity_allocated INTEGER NOT NULL DEFAULT 0,
    quantity_on_order INTEGER NOT NULL DEFAULT 0,
    reorder_point INTEGER NOT NULL DEFAULT 0,
    reorder_quantity INTEGER NOT NULL DEFAULT 0,
    safety_stock INTEGER NOT NULL DEFAULT 0,
    last_count_date TEXT,
    last_movement_date TEXT,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE,
    UNIQUE(product_code, warehouse_location)
);

-- =====================================================
-- STOCK ALLOCATIONS TABLE
-- =====================================================
CREATE TABLE stock_allocations (
    allocation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT NOT NULL,
    warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
    order_reference TEXT NOT NULL,
    allocated_quantity INTEGER NOT NULL,
    allocation_date TEXT NOT NULL DEFAULT (datetime('now')),
    expiry_date TEXT,
    status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'FULFILLED', 'EXPIRED', 'CANCELLED')) DEFAULT 'ACTIVE',
    created_by TEXT,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
);

-- =====================================================
-- SUPPLIER LEAD TIMES TABLE
-- =====================================================
CREATE TABLE supplier_lead_times (
    lead_time_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT NOT NULL,
    supplier_name TEXT NOT NULL,
    supplier_code TEXT,
    lead_time_days INTEGER NOT NULL,
    minimum_order_quantity INTEGER NOT NULL DEFAULT 1,
    price_per_unit REAL,
    is_preferred BOOLEAN NOT NULL DEFAULT 0,
    effective_date TEXT NOT NULL DEFAULT (datetime('now')),
    end_date TEXT,
    created_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
);

-- =====================================================
-- CUSTOMER CONTRACTS TABLE
-- =====================================================
CREATE TABLE customer_contracts (
    contract_id TEXT PRIMARY KEY,
    customer_id TEXT NOT NULL,
    contract_name TEXT NOT NULL,
    contract_type TEXT NOT NULL CHECK (contract_type IN ('STANDARD', 'VOLUME', 'ENTERPRISE', 'CUSTOM')),
    status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'PENDING')),
    effective_date TEXT NOT NULL,
    expiration_date TEXT,
    auto_renewal BOOLEAN NOT NULL DEFAULT 0,

    -- Pricing Terms
    base_discount_percent REAL NOT NULL DEFAULT 0.0,
    volume_discount_tier TEXT,
    payment_terms TEXT NOT NULL DEFAULT 'Net 30',
    currency TEXT NOT NULL DEFAULT 'USD',

    -- Contract Limits
    minimum_order_value REAL DEFAULT 0.0,
    maximum_order_value REAL,
    annual_commitment REAL,

    -- Approval Requirements
    requires_approval_above REAL,
    approval_workflow TEXT,

    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,

    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);

-- =====================================================
-- CONTRACT PRICING RULES TABLE
-- =====================================================
CREATE TABLE contract_pricing_rules (
    rule_id INTEGER PRIMARY KEY AUTOINCREMENT,
    contract_id TEXT NOT NULL,
    product_code TEXT,
    product_category TEXT,

    -- Pricing Rule Details
    rule_type TEXT NOT NULL CHECK (rule_type IN ('FIXED_PRICE', 'DISCOUNT_PERCENT', 'VOLUME_TIER', 'MARGIN_BASED')),
    rule_value REAL NOT NULL,
    minimum_quantity INTEGER DEFAULT 1,
    maximum_quantity INTEGER,

    -- Volume Tiers
    tier_name TEXT,
    tier_threshold REAL,

    -- Effective Period
    effective_date TEXT NOT NULL DEFAULT (datetime('now')),
    expiration_date TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,

    created_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (contract_id) REFERENCES customer_contracts(contract_id) ON DELETE CASCADE,
    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
);

-- =====================================================
-- PRICING MATRICES TABLE
-- =====================================================
CREATE TABLE pricing_matrices (
    matrix_id INTEGER PRIMARY KEY AUTOINCREMENT,
    matrix_name TEXT NOT NULL,
    matrix_type TEXT NOT NULL CHECK (matrix_type IN ('VOLUME', 'CUSTOMER_TIER', 'PRODUCT_MIX', 'SEASONAL')),

    -- Matrix Parameters
    customer_tier TEXT,
    product_category TEXT,
    region TEXT,
    season TEXT,

    -- Pricing Rules
    base_discount_percent REAL NOT NULL DEFAULT 0.0,
    volume_threshold REAL,
    volume_discount_percent REAL,

    -- Effective Period
    effective_date TEXT NOT NULL DEFAULT (datetime('now')),
    expiration_date TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,

    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now'))
);

-- =====================================================
-- MARGIN TARGETS TABLE
-- =====================================================
CREATE TABLE margin_targets (
    target_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT,
    product_category TEXT,
    customer_tier TEXT,

    -- Margin Requirements
    minimum_margin_percent REAL NOT NULL,
    target_margin_percent REAL NOT NULL,
    maximum_discount_percent REAL NOT NULL,

    -- Approval Thresholds
    requires_approval_below REAL,
    auto_reject_below REAL,

    -- Effective Period
    effective_date TEXT NOT NULL DEFAULT (datetime('now')),
    expiration_date TEXT,
    is_active BOOLEAN NOT NULL DEFAULT 1,

    created_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
);

-- =====================================================
-- PRICING APPROVALS TABLE
-- =====================================================
CREATE TABLE pricing_approvals (
    approval_id TEXT PRIMARY KEY,
    order_reference TEXT NOT NULL,
    customer_id TEXT NOT NULL,

    -- Pricing Details
    original_total REAL NOT NULL,
    proposed_total REAL NOT NULL,
    discount_amount REAL NOT NULL,
    discount_percent REAL NOT NULL,
    margin_percent REAL,

    -- Approval Status
    status TEXT NOT NULL CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED')),
    approval_level TEXT NOT NULL,
    approved_by TEXT,
    approval_date TEXT,
    rejection_reason TEXT,

    -- Request Details
    requested_by TEXT NOT NULL,
    request_date TEXT NOT NULL DEFAULT (datetime('now')),
    business_justification TEXT,
    expiry_date TEXT,

    created_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);

-- =====================================================
-- BACKORDERS TABLE
-- =====================================================
CREATE TABLE backorders (
    backorder_id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_reference TEXT NOT NULL,
    product_code TEXT NOT NULL,
    customer_id TEXT NOT NULL,
    requested_quantity INTEGER NOT NULL,
    backordered_quantity INTEGER NOT NULL,
    priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')) DEFAULT 'MEDIUM',
    expected_availability_date TEXT,
    customer_notification_sent BOOLEAN NOT NULL DEFAULT 0,
    status TEXT NOT NULL CHECK (status IN ('PENDING', 'PARTIAL', 'FULFILLED', 'CANCELLED')) DEFAULT 'PENDING',
    created_date TEXT NOT NULL DEFAULT (datetime('now')),
    updated_date TEXT NOT NULL DEFAULT (datetime('now')),

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);

-- =====================================================
-- INVENTORY TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE inventory_transactions (
    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code TEXT NOT NULL,
    warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('RECEIPT', 'SHIPMENT', 'ADJUSTMENT', 'ALLOCATION', 'DEALLOCATION', 'TRANSFER')),
    quantity_change INTEGER NOT NULL,
    reference_number TEXT,
    order_reference TEXT,
    reason_code TEXT,
    notes TEXT,
    transaction_date TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,

    FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
);

-- =====================================================
-- INVENTORY INDEXES FOR PERFORMANCE
-- =====================================================

-- Inventory levels indexes
CREATE INDEX idx_inventory_product ON inventory_levels(product_code);
CREATE INDEX idx_inventory_warehouse ON inventory_levels(warehouse_location);
CREATE INDEX idx_inventory_available ON inventory_levels(quantity_available);

-- Stock allocations indexes
CREATE INDEX idx_allocations_product ON stock_allocations(product_code);
CREATE INDEX idx_allocations_order ON stock_allocations(order_reference);
CREATE INDEX idx_allocations_status ON stock_allocations(status);
CREATE INDEX idx_allocations_date ON stock_allocations(allocation_date);

-- Supplier lead times indexes
CREATE INDEX idx_leadtime_product ON supplier_lead_times(product_code);
CREATE INDEX idx_leadtime_supplier ON supplier_lead_times(supplier_name);
CREATE INDEX idx_leadtime_preferred ON supplier_lead_times(is_preferred);

-- Backorders indexes
CREATE INDEX idx_backorders_product ON backorders(product_code);
CREATE INDEX idx_backorders_customer ON backorders(customer_id);
CREATE INDEX idx_backorders_status ON backorders(status);
CREATE INDEX idx_backorders_priority ON backorders(priority_level);

-- Inventory transactions indexes
CREATE INDEX idx_transactions_product ON inventory_transactions(product_code);
CREATE INDEX idx_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX idx_transactions_date ON inventory_transactions(transaction_date);
CREATE INDEX idx_transactions_reference ON inventory_transactions(reference_number);

-- =====================================================
-- PRICING & CONTRACT INDEXES FOR PERFORMANCE
-- =====================================================

-- Customer contracts indexes
CREATE INDEX idx_contracts_customer ON customer_contracts(customer_id);
CREATE INDEX idx_contracts_status ON customer_contracts(status);
CREATE INDEX idx_contracts_effective ON customer_contracts(effective_date);
CREATE INDEX idx_contracts_expiration ON customer_contracts(expiration_date);

-- Contract pricing rules indexes
CREATE INDEX idx_pricing_rules_contract ON contract_pricing_rules(contract_id);
CREATE INDEX idx_pricing_rules_product ON contract_pricing_rules(product_code);
CREATE INDEX idx_pricing_rules_category ON contract_pricing_rules(product_category);
CREATE INDEX idx_pricing_rules_active ON contract_pricing_rules(is_active);

-- Pricing matrices indexes
CREATE INDEX idx_pricing_matrices_type ON pricing_matrices(matrix_type);
CREATE INDEX idx_pricing_matrices_tier ON pricing_matrices(customer_tier);
CREATE INDEX idx_pricing_matrices_category ON pricing_matrices(product_category);
CREATE INDEX idx_pricing_matrices_active ON pricing_matrices(is_active);

-- Margin targets indexes
CREATE INDEX idx_margin_targets_product ON margin_targets(product_code);
CREATE INDEX idx_margin_targets_category ON margin_targets(product_category);
CREATE INDEX idx_margin_targets_tier ON margin_targets(customer_tier);
CREATE INDEX idx_margin_targets_active ON margin_targets(is_active);

-- Pricing approvals indexes
CREATE INDEX idx_pricing_approvals_customer ON pricing_approvals(customer_id);
CREATE INDEX idx_pricing_approvals_status ON pricing_approvals(status);
CREATE INDEX idx_pricing_approvals_order ON pricing_approvals(order_reference);
CREATE INDEX idx_pricing_approvals_date ON pricing_approvals(request_date);

-- =====================================================
-- INVENTORY TRIGGERS FOR UPDATED_DATE
-- =====================================================

-- Update inventory_levels updated_date on changes
CREATE TRIGGER update_inventory_levels_timestamp
    AFTER UPDATE ON inventory_levels
    FOR EACH ROW
BEGIN
    UPDATE inventory_levels SET updated_date = datetime('now') WHERE inventory_id = NEW.inventory_id;
END;

-- Update backorders updated_date on changes
CREATE TRIGGER update_backorders_timestamp
    AFTER UPDATE ON backorders
    FOR EACH ROW
BEGIN
    UPDATE backorders SET updated_date = datetime('now') WHERE backorder_id = NEW.backorder_id;
END;

-- =====================================================
-- PRICING & CONTRACT TRIGGERS FOR UPDATED_DATE
-- =====================================================

-- Update customer_contracts updated_date on changes
CREATE TRIGGER update_contracts_timestamp
    AFTER UPDATE ON customer_contracts
    FOR EACH ROW
BEGIN
    UPDATE customer_contracts SET updated_date = datetime('now') WHERE contract_id = NEW.contract_id;
END;

-- Update pricing_matrices updated_date on changes
CREATE TRIGGER update_pricing_matrices_timestamp
    AFTER UPDATE ON pricing_matrices
    FOR EACH ROW
BEGIN
    UPDATE pricing_matrices SET updated_date = datetime('now') WHERE matrix_id = NEW.matrix_id;
END;

-- =====================================================
-- INVENTORY TRIGGERS FOR AUTOMATIC CALCULATIONS
-- =====================================================

-- Automatically update quantity_available when allocations change
CREATE TRIGGER update_available_quantity_on_allocation
    AFTER INSERT ON stock_allocations
    FOR EACH ROW
    WHEN NEW.status = 'ACTIVE'
BEGIN
    UPDATE inventory_levels
    SET quantity_allocated = quantity_allocated + NEW.allocated_quantity,
        quantity_available = quantity_on_hand - (quantity_allocated + NEW.allocated_quantity)
    WHERE product_code = NEW.product_code AND warehouse_location = NEW.warehouse_location;
END;

-- Automatically update quantity_available when allocations are removed
CREATE TRIGGER update_available_quantity_on_deallocation
    AFTER UPDATE ON stock_allocations
    FOR EACH ROW
    WHEN OLD.status = 'ACTIVE' AND NEW.status IN ('FULFILLED', 'EXPIRED', 'CANCELLED')
BEGIN
    UPDATE inventory_levels
    SET quantity_allocated = quantity_allocated - OLD.allocated_quantity,
        quantity_available = quantity_on_hand - (quantity_allocated - OLD.allocated_quantity)
    WHERE product_code = OLD.product_code AND warehouse_location = OLD.warehouse_location;
END;
