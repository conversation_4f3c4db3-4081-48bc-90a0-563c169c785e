"""
Dynamic Pricing & Contract Management Tools for O2C System

This module provides comprehensive tools for:
- Contract retrieval and management
- Dynamic pricing calculations
- Volume discount processing
- Margin analysis and optimization
- Pricing approval workflows
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Import tool decorator
from crewai.tools import tool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
USE_DATABASE = True
try:
    from database.database_manager import DatabaseManager
    db = DatabaseManager()
except ImportError:
    USE_DATABASE = False
    db = None
    logger.warning("Database not available, using mock data")


@tool
def contract_retriever(customer_name: str, customer_id: str = None) -> str:
    """Retrieve customer contract terms and pricing agreements
    
    Args:
        customer_name: Name of the customer
        customer_id: Optional customer ID for more precise lookup
        
    Returns:
        Customer contract details including pricing terms, discounts, and conditions
    """
    try:
        logger.info(f" Retrieving contract for customer: {customer_name}")
        
        # Mock contract data for demonstration
        mock_contracts = {
            "Acme Corporation": {
                "contract_id": "CONT-ACME-2024-001",
                "contract_type": "ENTERPRISE",
                "status": "ACTIVE",
                "effective_date": "2024-01-01",
                "expiration_date": "2024-12-31",
                "base_discount_percent": 15.0,
                "volume_discount_tier": "TIER_2",
                "payment_terms": "Net 30",
                "currency": "USD",
                "minimum_order_value": 1000.0,
                "maximum_order_value": 100000.0,
                "annual_commitment": 500000.0,
                "requires_approval_above": 50000.0,
                "pricing_rules": [
                    {
                        "rule_type": "VOLUME_TIER",
                        "product_category": "WIDGETS",
                        "tier_threshold": 100,
                        "discount_percent": 20.0
                    },
                    {
                        "rule_type": "FIXED_PRICE",
                        "product_code": "WIDGET-A100",
                        "fixed_price": 110.0
                    }
                ]
            },
            "XYZ Corp": {
                "contract_id": "CONT-XYZ-2024-002",
                "contract_type": "VOLUME",
                "status": "ACTIVE",
                "effective_date": "2024-03-01",
                "expiration_date": "2025-02-28",
                "base_discount_percent": 8.0,
                "volume_discount_tier": "TIER_1",
                "payment_terms": "Net 15",
                "currency": "USD",
                "minimum_order_value": 500.0,
                "maximum_order_value": 25000.0,
                "annual_commitment": 150000.0,
                "requires_approval_above": 20000.0,
                "pricing_rules": [
                    {
                        "rule_type": "DISCOUNT_PERCENT",
                        "product_category": "BOLTS",
                        "discount_percent": 12.0
                    }
                ]
            }
        }
        
        # Get contract data from database or fallback to mock data
        if USE_DATABASE and db:
            # Try to get from database first
            contract_data = db.get_customer_contract(customer_name)
            if not contract_data:
                contract_data = mock_contracts.get(customer_name)
        else:
            contract_data = mock_contracts.get(customer_name)
        
        if not contract_data:
            # Return standard terms for customers without contracts
            contract_data = {
                "contract_id": f"STD-{customer_name.upper().replace(' ', '-')}",
                "contract_type": "STANDARD",
                "status": "ACTIVE",
                "base_discount_percent": 0.0,
                "payment_terms": "Net 30",
                "currency": "USD",
                "requires_approval_above": 10000.0,
                "pricing_rules": []
            }
        
        result = {
            "customer_name": customer_name,
            "contract_found": contract_data is not None,
            "contract_details": contract_data,
            "retrieval_timestamp": datetime.now().isoformat()
        }
        
        logger.info(f" Contract retrieved for {customer_name}: {contract_data.get('contract_type', 'STANDARD')}")
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f" Error retrieving contract for {customer_name}: {str(e)}")
        return json.dumps({
            "error": f"Contract retrieval failed: {str(e)}",
            "customer_name": customer_name,
            "contract_found": False
        })


@tool
def pricing_calculator(order_data: Dict[str, Any], contract_terms: Dict[str, Any]) -> str:
    """Calculate optimized pricing based on contract terms and business rules
    
    Args:
        order_data: Order details including line items and quantities
        contract_terms: Customer contract terms and pricing rules
        
    Returns:
        Detailed pricing calculations with discounts and adjustments
    """
    try:
        logger.info(f"💰 Calculating pricing for order: {order_data.get('po_number', 'UNKNOWN')}")
        
        line_items = order_data.get('line_items', [])
        contract_details = contract_terms.get('contract_details', {})
        
        pricing_results = {
            "order_reference": order_data.get('po_number'),
            "customer_name": order_data.get('customer_info', {}).get('name'),
            "contract_id": contract_details.get('contract_id'),
            "currency": contract_details.get('currency', 'USD'),
            "line_item_pricing": [],
            "pricing_summary": {},
            "discounts_applied": [],
            "margin_analysis": {}
        }
        
        total_original = 0.0
        total_discounted = 0.0
        total_discount_amount = 0.0
        
        # Process each line item
        for item in line_items:
            product_code = item.get('product_code')
            quantity = item.get('quantity', 0)
            unit_price = item.get('unit_price', 0.0)
            line_total = quantity * unit_price
            
            # Calculate discounts
            item_discount_percent = 0.0
            applied_discounts = []
            
            # Apply base contract discount
            base_discount = contract_details.get('base_discount_percent', 0.0)
            if base_discount > 0:
                item_discount_percent += base_discount
                applied_discounts.append({
                    "type": "BASE_CONTRACT",
                    "percent": base_discount,
                    "description": f"Base contract discount"
                })
            
            # Apply product-specific pricing rules
            pricing_rules = contract_details.get('pricing_rules', [])
            for rule in pricing_rules:
                if rule.get('product_code') == product_code:
                    if rule.get('rule_type') == 'FIXED_PRICE':
                        unit_price = rule.get('fixed_price', unit_price)
                        applied_discounts.append({
                            "type": "FIXED_PRICE",
                            "new_price": unit_price,
                            "description": f"Contract fixed price"
                        })
                    elif rule.get('rule_type') == 'DISCOUNT_PERCENT':
                        rule_discount = rule.get('discount_percent', 0.0)
                        item_discount_percent += rule_discount
                        applied_discounts.append({
                            "type": "PRODUCT_DISCOUNT",
                            "percent": rule_discount,
                            "description": f"Product-specific discount"
                        })
                elif rule.get('product_category') and item.get('category') == rule.get('product_category'):
                    if rule.get('rule_type') == 'VOLUME_TIER':
                        tier_threshold = rule.get('tier_threshold', 0)
                        if quantity >= tier_threshold:
                            tier_discount = rule.get('discount_percent', 0.0)
                            item_discount_percent += tier_discount
                            applied_discounts.append({
                                "type": "VOLUME_TIER",
                                "percent": tier_discount,
                                "threshold": tier_threshold,
                                "description": f"Volume tier discount (qty >= {tier_threshold})"
                            })
            
            # Calculate final pricing
            discounted_unit_price = unit_price * (1 - item_discount_percent / 100)
            discounted_line_total = quantity * discounted_unit_price
            line_discount_amount = line_total - discounted_line_total
            
            line_pricing = {
                "product_code": product_code,
                "quantity": quantity,
                "original_unit_price": unit_price,
                "discounted_unit_price": discounted_unit_price,
                "original_line_total": line_total,
                "discounted_line_total": discounted_line_total,
                "discount_amount": line_discount_amount,
                "discount_percent": item_discount_percent,
                "applied_discounts": applied_discounts
            }
            
            pricing_results["line_item_pricing"].append(line_pricing)
            
            total_original += line_total
            total_discounted += discounted_line_total
            total_discount_amount += line_discount_amount
        
        # Calculate summary
        total_discount_percent = (total_discount_amount / total_original * 100) if total_original > 0 else 0
        
        pricing_results["pricing_summary"] = {
            "original_total": total_original,
            "discounted_total": total_discounted,
            "total_discount_amount": total_discount_amount,
            "total_discount_percent": total_discount_percent,
            "currency": contract_details.get('currency', 'USD')
        }
        
        # Margin analysis (simplified)
        estimated_cost = total_discounted * 0.6  # Assume 60% cost ratio
        estimated_margin = total_discounted - estimated_cost
        margin_percent = (estimated_margin / total_discounted * 100) if total_discounted > 0 else 0
        
        pricing_results["margin_analysis"] = {
            "estimated_cost": estimated_cost,
            "estimated_margin": estimated_margin,
            "margin_percent": margin_percent,
            "margin_status": "ACCEPTABLE" if margin_percent >= 25 else "LOW"
        }
        
        logger.info(f" Pricing calculated: ${total_discounted:,.2f} (discount: {total_discount_percent:.1f}%)")
        return json.dumps(pricing_results, indent=2)
        
    except Exception as e:
        logger.error(f" Error calculating pricing: {str(e)}")
        return json.dumps({
            "error": f"Pricing calculation failed: {str(e)}",
            "order_reference": order_data.get('po_number', 'UNKNOWN')
        })


@tool
def volume_discount_analyzer(order_data: Dict[str, Any], customer_history: Dict[str, Any] = None) -> str:
    """Analyze volume discounts and tier eligibility

    Args:
        order_data: Current order details
        customer_history: Optional customer purchase history

    Returns:
        Volume discount analysis and recommendations
    """
    try:
        logger.info(f"📊 Analyzing volume discounts for order: {order_data.get('po_number', 'UNKNOWN')}")

        line_items = order_data.get('line_items', [])
        customer_name = order_data.get('customer_info', {}).get('name', 'Unknown')

        # Volume discount tiers (mock data)
        volume_tiers = {
            "TIER_1": {"min_quantity": 50, "discount_percent": 5.0},
            "TIER_2": {"min_quantity": 100, "discount_percent": 10.0},
            "TIER_3": {"min_quantity": 250, "discount_percent": 15.0},
            "TIER_4": {"min_quantity": 500, "discount_percent": 20.0}
        }

        analysis_results = {
            "customer_name": customer_name,
            "order_reference": order_data.get('po_number'),
            "volume_analysis": [],
            "tier_recommendations": [],
            "potential_savings": 0.0
        }

        total_potential_savings = 0.0

        # Analyze each line item for volume discounts
        for item in line_items:
            product_code = item.get('product_code')
            quantity = item.get('quantity', 0)
            unit_price = item.get('unit_price', 0.0)
            line_total = quantity * unit_price

            # Determine current tier
            current_tier = None
            current_discount = 0.0

            for tier_name, tier_info in volume_tiers.items():
                if quantity >= tier_info["min_quantity"]:
                    current_tier = tier_name
                    current_discount = tier_info["discount_percent"]

            # Find next tier opportunity
            next_tier = None
            next_tier_quantity = None
            next_tier_discount = None

            for tier_name, tier_info in volume_tiers.items():
                if quantity < tier_info["min_quantity"]:
                    if next_tier is None or tier_info["min_quantity"] < volume_tiers[next_tier]["min_quantity"]:
                        next_tier = tier_name
                        next_tier_quantity = tier_info["min_quantity"]
                        next_tier_discount = tier_info["discount_percent"]

            # Calculate potential savings
            if next_tier:
                additional_quantity = next_tier_quantity - quantity
                additional_cost = additional_quantity * unit_price
                current_discount_amount = line_total * (current_discount / 100)
                next_tier_discount_amount = (line_total + additional_cost) * (next_tier_discount / 100)
                potential_savings = next_tier_discount_amount - current_discount_amount - additional_cost

                if potential_savings > 0:
                    total_potential_savings += potential_savings
            else:
                potential_savings = 0.0

            item_analysis = {
                "product_code": product_code,
                "quantity": quantity,
                "unit_price": unit_price,
                "line_total": line_total,
                "current_tier": current_tier,
                "current_discount_percent": current_discount,
                "next_tier": next_tier,
                "next_tier_quantity": next_tier_quantity,
                "additional_quantity_needed": next_tier_quantity - quantity if next_tier else 0,
                "potential_savings": potential_savings
            }

            analysis_results["volume_analysis"].append(item_analysis)

            # Add recommendation if savings are significant
            if potential_savings > 50:  # Threshold for recommendation
                analysis_results["tier_recommendations"].append({
                    "product_code": product_code,
                    "recommendation": f"Consider increasing quantity to {next_tier_quantity} for {next_tier} discount",
                    "additional_quantity": next_tier_quantity - quantity,
                    "potential_savings": potential_savings,
                    "new_discount_percent": next_tier_discount
                })

        analysis_results["potential_savings"] = total_potential_savings
        analysis_results["analysis_timestamp"] = datetime.now().isoformat()

        logger.info(f" Volume analysis complete. Potential savings: ${total_potential_savings:.2f}")
        return json.dumps(analysis_results, indent=2)

    except Exception as e:
        logger.error(f" Error analyzing volume discounts: {str(e)}")
        return json.dumps({
            "error": f"Volume discount analysis failed: {str(e)}",
            "order_reference": order_data.get('po_number', 'UNKNOWN')
        })


@tool
def margin_optimizer(pricing_data: Dict[str, Any], margin_targets: Dict[str, Any] = None) -> str:
    """Optimize pricing to meet margin targets while remaining competitive

    Args:
        pricing_data: Current pricing calculations
        margin_targets: Target margin requirements by product/category

    Returns:
        Margin optimization recommendations and adjusted pricing
    """
    try:
        logger.info(f"🎯 Optimizing margins for order: {pricing_data.get('order_reference', 'UNKNOWN')}")

        # Default margin targets
        default_targets = {
            "WIDGETS": {"minimum_margin": 20.0, "target_margin": 30.0, "maximum_discount": 25.0},
            "BOLTS": {"minimum_margin": 15.0, "target_margin": 25.0, "maximum_discount": 20.0},
            "GASKETS": {"minimum_margin": 25.0, "target_margin": 35.0, "maximum_discount": 15.0}
        }

        if not margin_targets:
            margin_targets = default_targets

        line_items = pricing_data.get('line_item_pricing', [])
        current_margin = pricing_data.get('margin_analysis', {})

        optimization_results = {
            "order_reference": pricing_data.get('order_reference'),
            "current_margin_percent": current_margin.get('margin_percent', 0),
            "target_margin_percent": 25.0,  # Default target
            "optimization_recommendations": [],
            "adjusted_pricing": [],
            "margin_improvement": 0.0,
            "requires_approval": False
        }

        total_current_revenue = 0.0
        total_optimized_revenue = 0.0

        for item in line_items:
            product_code = item.get('product_code', '')
            category = product_code.split('-')[0] if '-' in product_code else 'DEFAULT'

            # Get margin targets for this category
            targets = margin_targets.get(category, margin_targets.get('DEFAULT', {
                "minimum_margin": 20.0, "target_margin": 25.0, "maximum_discount": 20.0
            }))

            current_price = item.get('discounted_unit_price', 0)
            quantity = item.get('quantity', 0)
            current_revenue = current_price * quantity

            # Estimate cost (simplified - in real system would come from cost database)
            estimated_cost_per_unit = current_price * 0.6  # Assume 60% cost ratio

            # Calculate target price for desired margin
            target_margin = targets.get('target_margin', 25.0)
            target_price = estimated_cost_per_unit / (1 - target_margin / 100)

            # Check if current pricing meets minimum margin
            current_margin_per_unit = current_price - estimated_cost_per_unit
            current_margin_percent = (current_margin_per_unit / current_price * 100) if current_price > 0 else 0

            minimum_margin = targets.get('minimum_margin', 20.0)
            margin_status = "ACCEPTABLE" if current_margin_percent >= minimum_margin else "BELOW_MINIMUM"

            # Determine optimization action
            if current_margin_percent < minimum_margin:
                # Need to increase price or reduce discount
                minimum_price = estimated_cost_per_unit / (1 - minimum_margin / 100)
                recommended_action = "INCREASE_PRICE"
                recommended_price = max(minimum_price, target_price)
            elif current_margin_percent < target_margin:
                # Could optimize for better margin
                recommended_action = "OPTIMIZE_MARGIN"
                recommended_price = target_price
            else:
                # Margin is acceptable
                recommended_action = "MAINTAIN_PRICE"
                recommended_price = current_price

            optimized_revenue = recommended_price * quantity

            item_optimization = {
                "product_code": product_code,
                "category": category,
                "current_price": current_price,
                "recommended_price": recommended_price,
                "current_margin_percent": current_margin_percent,
                "target_margin_percent": target_margin,
                "margin_status": margin_status,
                "recommended_action": recommended_action,
                "revenue_impact": optimized_revenue - current_revenue
            }

            optimization_results["optimization_recommendations"].append(item_optimization)
            optimization_results["adjusted_pricing"].append({
                "product_code": product_code,
                "quantity": quantity,
                "original_unit_price": item.get('original_unit_price'),
                "current_unit_price": current_price,
                "optimized_unit_price": recommended_price,
                "margin_percent": (recommended_price - estimated_cost_per_unit) / recommended_price * 100
            })

            total_current_revenue += current_revenue
            total_optimized_revenue += optimized_revenue

        # Calculate overall improvement
        margin_improvement = total_optimized_revenue - total_current_revenue
        improvement_percent = (margin_improvement / total_current_revenue * 100) if total_current_revenue > 0 else 0

        optimization_results["margin_improvement"] = margin_improvement
        optimization_results["improvement_percent"] = improvement_percent
        optimization_results["requires_approval"] = improvement_percent > 10  # Significant price changes need approval

        logger.info(f" Margin optimization complete. Improvement: ${margin_improvement:.2f} ({improvement_percent:.1f}%)")
        return json.dumps(optimization_results, indent=2)

    except Exception as e:
        logger.error(f" Error optimizing margins: {str(e)}")
        return json.dumps({
            "error": f"Margin optimization failed: {str(e)}",
            "order_reference": pricing_data.get('order_reference', 'UNKNOWN')
        })


@tool
def contract_compliance_checker(pricing_data: Dict[str, Any], contract_terms: Dict[str, Any]) -> str:
    """Verify pricing compliance with contract terms and conditions

    Args:
        pricing_data: Calculated pricing information
        contract_terms: Customer contract terms and limits

    Returns:
        Contract compliance verification results
    """
    try:
        logger.info(f" Checking contract compliance for order: {pricing_data.get('order_reference', 'UNKNOWN')}")

        contract_details = contract_terms.get('contract_details', {})
        pricing_summary = pricing_data.get('pricing_summary', {})

        compliance_results = {
            "order_reference": pricing_data.get('order_reference'),
            "contract_id": contract_details.get('contract_id'),
            "compliance_status": "COMPLIANT",
            "compliance_checks": [],
            "violations": [],
            "warnings": [],
            "approval_required": False
        }

        order_total = pricing_summary.get('discounted_total', 0)

        # Check minimum order value
        min_order_value = contract_details.get('minimum_order_value')
        if min_order_value and order_total < min_order_value:
            violation = {
                "type": "MINIMUM_ORDER_VALUE",
                "description": f"Order total ${order_total:,.2f} below minimum ${min_order_value:,.2f}",
                "severity": "ERROR",
                "current_value": order_total,
                "required_value": min_order_value
            }
            compliance_results["violations"].append(violation)
            compliance_results["compliance_status"] = "NON_COMPLIANT"

        # Check maximum order value
        max_order_value = contract_details.get('maximum_order_value')
        if max_order_value and order_total > max_order_value:
            violation = {
                "type": "MAXIMUM_ORDER_VALUE",
                "description": f"Order total ${order_total:,.2f} exceeds maximum ${max_order_value:,.2f}",
                "severity": "ERROR",
                "current_value": order_total,
                "required_value": max_order_value
            }
            compliance_results["violations"].append(violation)
            compliance_results["compliance_status"] = "NON_COMPLIANT"

        # Check approval threshold
        approval_threshold = contract_details.get('requires_approval_above')
        if approval_threshold and order_total > approval_threshold:
            warning = {
                "type": "APPROVAL_REQUIRED",
                "description": f"Order total ${order_total:,.2f} requires approval (threshold: ${approval_threshold:,.2f})",
                "severity": "WARNING",
                "current_value": order_total,
                "threshold": approval_threshold
            }
            compliance_results["warnings"].append(warning)
            compliance_results["approval_required"] = True

        # Check contract expiration
        expiration_date = contract_details.get('expiration_date')
        if expiration_date:
            try:
                exp_date = datetime.fromisoformat(expiration_date.replace('Z', '+00:00'))
                if datetime.now() > exp_date:
                    violation = {
                        "type": "CONTRACT_EXPIRED",
                        "description": f"Contract expired on {expiration_date}",
                        "severity": "ERROR",
                        "expiration_date": expiration_date
                    }
                    compliance_results["violations"].append(violation)
                    compliance_results["compliance_status"] = "NON_COMPLIANT"
                elif (exp_date - datetime.now()).days <= 30:
                    warning = {
                        "type": "CONTRACT_EXPIRING",
                        "description": f"Contract expires soon on {expiration_date}",
                        "severity": "WARNING",
                        "expiration_date": expiration_date
                    }
                    compliance_results["warnings"].append(warning)
            except ValueError:
                pass  # Invalid date format

        # Check currency compliance
        contract_currency = contract_details.get('currency', 'USD')
        pricing_currency = pricing_summary.get('currency', 'USD')
        if contract_currency != pricing_currency:
            violation = {
                "type": "CURRENCY_MISMATCH",
                "description": f"Pricing currency {pricing_currency} doesn't match contract currency {contract_currency}",
                "severity": "ERROR",
                "contract_currency": contract_currency,
                "pricing_currency": pricing_currency
            }
            compliance_results["violations"].append(violation)
            compliance_results["compliance_status"] = "NON_COMPLIANT"

        # Add successful compliance checks
        compliance_results["compliance_checks"] = [
            {"check": "minimum_order_value", "status": "PASS" if not any(v["type"] == "MINIMUM_ORDER_VALUE" for v in compliance_results["violations"]) else "FAIL"},
            {"check": "maximum_order_value", "status": "PASS" if not any(v["type"] == "MAXIMUM_ORDER_VALUE" for v in compliance_results["violations"]) else "FAIL"},
            {"check": "contract_validity", "status": "PASS" if not any(v["type"] == "CONTRACT_EXPIRED" for v in compliance_results["violations"]) else "FAIL"},
            {"check": "currency_compliance", "status": "PASS" if not any(v["type"] == "CURRENCY_MISMATCH" for v in compliance_results["violations"]) else "FAIL"}
        ]

        compliance_results["check_timestamp"] = datetime.now().isoformat()

        logger.info(f" Contract compliance check complete: {compliance_results['compliance_status']}")
        return json.dumps(compliance_results, indent=2)

    except Exception as e:
        logger.error(f" Error checking contract compliance: {str(e)}")
        return json.dumps({
            "error": f"Contract compliance check failed: {str(e)}",
            "order_reference": pricing_data.get('order_reference', 'UNKNOWN'),
            "compliance_status": "ERROR"
        })


@tool
def pricing_approval_workflow(pricing_data: Dict[str, Any], approval_requirements: Dict[str, Any] = None) -> str:
    """Manage pricing approval workflow for orders requiring authorization

    Args:
        pricing_data: Pricing calculations and margin analysis
        approval_requirements: Approval thresholds and workflow rules

    Returns:
        Approval workflow status and next steps
    """
    try:
        logger.info(f"🔐 Processing pricing approval workflow for order: {pricing_data.get('order_reference', 'UNKNOWN')}")

        # Default approval requirements
        default_requirements = {
            "auto_approve_below": 10000.0,
            "manager_approval_below": 50000.0,
            "director_approval_below": 100000.0,
            "vp_approval_required": 100000.0,
            "minimum_margin_threshold": 15.0,
            "discount_threshold": 20.0
        }

        if not approval_requirements:
            approval_requirements = default_requirements

        order_total = pricing_data.get('pricing_summary', {}).get('discounted_total', 0)
        margin_percent = pricing_data.get('margin_analysis', {}).get('margin_percent', 0)
        discount_percent = pricing_data.get('pricing_summary', {}).get('total_discount_percent', 0)

        workflow_result = {
            "order_reference": pricing_data.get('order_reference'),
            "order_total": order_total,
            "margin_percent": margin_percent,
            "discount_percent": discount_percent,
            "approval_status": "AUTO_APPROVED",
            "approval_level": "SYSTEM",
            "approval_reasons": [],
            "next_steps": [],
            "estimated_approval_time": "IMMEDIATE"
        }

        # Determine approval requirements
        approval_needed = False
        approval_reasons = []

        # Check order value thresholds
        if order_total >= approval_requirements.get('vp_approval_required', 100000):
            approval_needed = True
            workflow_result["approval_level"] = "VP"
            workflow_result["estimated_approval_time"] = "2-3 business days"
            approval_reasons.append(f"Order value ${order_total:,.2f} requires VP approval")
        elif order_total >= approval_requirements.get('director_approval_below', 50000):
            approval_needed = True
            workflow_result["approval_level"] = "DIRECTOR"
            workflow_result["estimated_approval_time"] = "1-2 business days"
            approval_reasons.append(f"Order value ${order_total:,.2f} requires Director approval")
        elif order_total >= approval_requirements.get('manager_approval_below', 10000):
            approval_needed = True
            workflow_result["approval_level"] = "MANAGER"
            workflow_result["estimated_approval_time"] = "4-8 hours"
            approval_reasons.append(f"Order value ${order_total:,.2f} requires Manager approval")

        # Check margin thresholds
        min_margin = approval_requirements.get('minimum_margin_threshold', 15.0)
        if margin_percent < min_margin:
            approval_needed = True
            if workflow_result["approval_level"] == "SYSTEM":
                workflow_result["approval_level"] = "MANAGER"
                workflow_result["estimated_approval_time"] = "4-8 hours"
            approval_reasons.append(f"Margin {margin_percent:.1f}% below minimum {min_margin}%")

        # Check discount thresholds
        max_discount = approval_requirements.get('discount_threshold', 20.0)
        if discount_percent > max_discount:
            approval_needed = True
            if workflow_result["approval_level"] in ["SYSTEM", "MANAGER"]:
                workflow_result["approval_level"] = "DIRECTOR"
                workflow_result["estimated_approval_time"] = "1-2 business days"
            approval_reasons.append(f"Discount {discount_percent:.1f}% exceeds threshold {max_discount}%")

        # Set final status
        if approval_needed:
            workflow_result["approval_status"] = "PENDING_APPROVAL"
            workflow_result["approval_reasons"] = approval_reasons

            # Generate approval ID
            approval_id = f"APR-{datetime.now().strftime('%Y%m%d%H%M%S')}-{pricing_data.get('order_reference', 'UNK')}"
            workflow_result["approval_id"] = approval_id

            # Define next steps
            workflow_result["next_steps"] = [
                f"Submit for {workflow_result['approval_level']} approval",
                "Provide business justification for pricing decisions",
                "Monitor approval status",
                f"Expected approval within {workflow_result['estimated_approval_time']}"
            ]
        else:
            workflow_result["next_steps"] = [
                "Pricing automatically approved",
                "Proceed to next stage in workflow",
                "Generate pricing documentation"
            ]

        workflow_result["workflow_timestamp"] = datetime.now().isoformat()

        logger.info(f" Pricing approval workflow complete: {workflow_result['approval_status']} ({workflow_result['approval_level']})")
        return json.dumps(workflow_result, indent=2)

    except Exception as e:
        logger.error(f" Error processing pricing approval workflow: {str(e)}")
        return json.dumps({
            "error": f"Pricing approval workflow failed: {str(e)}",
            "order_reference": pricing_data.get('order_reference', 'UNKNOWN'),
            "approval_status": "ERROR"
        })
