#!/usr/bin/env python3
"""
Setup script for CrewAI O2C System
Configures DeepSeek API and tests the setup
"""

import os
import json
import getpass
from agents.llm.deepseek_llm import test_deepseek_connection


def setup_deepseek_api():
    """Interactive setup for DeepSeek API"""
    print("=== DeepSeek API Setup ===\n")
    
    print("To use the CrewAI PO Parser Agent, you need a DeepSeek API key.")
    print("Get your API key from: https://platform.deepseek.com/api_keys\n")
    
    # Get API key
    api_key = getpass.getpass("Enter your DeepSeek API key: ").strip()
    
    if not api_key:
        print(" API key is required!")
        return False
    
    # Test the connection
    print("\n🔄 Testing DeepSeek API connection...")
    
    if test_deepseek_connection(api_key):
        print(" DeepSeek API connection successful!")
        
        # Save to environment (for current session)
        os.environ["DEEPSEEK_API_KEY"] = api_key
        
        # Update config file
        config_file = "config/deepseek_config.json"
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            config["deepseek"]["api_key"] = api_key
            
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f" Configuration saved to {config_file}")
            
        except Exception as e:
            print(f"⚠️ Warning: Could not save to config file: {str(e)}")
        
        # Provide instructions for permanent setup
        print("\n📝 To make this permanent, add to your shell profile:")
        print(f"export DEEPSEEK_API_KEY='{api_key}'")
        
        return True
    else:
        print(" DeepSeek API connection failed!")
        print("Please check your API key and try again.")
        return False


def check_dependencies():
    """Check if required dependencies are installed"""
    print("=== Checking Dependencies ===\n")
    
    required_packages = [
        "crewai",
        "langchain", 
        "openai",
        "requests",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f" {package}")
        except ImportError:
            print(f" {package} - NOT INSTALLED")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install -r requirements.txt")
        return False
    
    print("\n All dependencies are installed!")
    return True


def create_directories():
    """Create necessary directories"""
    print("=== Creating Directories ===\n")
    
    directories = [
        "data/incoming_pos",
        "data/parsed_pos", 
        "data/processed_pos",
        "logs",
        "agents/llm",
        "agents/tools"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f" {directory}")
    
    print("\n All directories created!")


def test_po_parsing_tools():
    """Test the PO parsing tools"""
    print("=== Testing PO Parsing Tools ===\n")
    
    try:
        from agents.tools.po_parsing_tools import get_po_parsing_tools
        
        tools = get_po_parsing_tools()
        print(f" Loaded {len(tools)} PO parsing tools:")
        
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # Test listing PO files
        list_tool = tools[3]  # POListTool
        result = list_tool._run()
        print(f"\n Available PO files:\n{result}")
        
        return True
        
    except Exception as e:
        print(f" Error testing tools: {str(e)}")
        return False


def show_usage_examples():
    """Show usage examples"""
    print("=== Usage Examples ===\n")
    
    print("1. Process most recent PO file:")
    print("   python o2c_crew.py")
    
    print("\n2. Process specific PO file:")
    print("   python o2c_crew.py PO_20240708_190646_23.txt")
    
    print("\n3. Process all pending PO files:")
    print("   python o2c_crew.py --all")
    
    print("\n4. Test individual agent:")
    print("   python agents/po_parser_agent.py")
    
    print("\n5. Check parsed results:")
    print("   ls -la data/parsed_pos/")


def main():
    """Main setup function"""
    print(" CrewAI O2C System Setup\n")
    
    # Check dependencies
    if not check_dependencies():
        print("\n Setup failed: Missing dependencies")
        print("Please install requirements: pip install -r requirements.txt")
        return
    
    # Create directories
    create_directories()
    
    # Setup DeepSeek API
    if not setup_deepseek_api():
        print("\n Setup failed: DeepSeek API configuration")
        return
    
    # Test tools
    if not test_po_parsing_tools():
        print("\n Setup failed: PO parsing tools")
        return
    
    print("\n" + "="*50)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("="*50)
    
    # Show usage examples
    show_usage_examples()
    
    print("\n Your CrewAI O2C System is ready to use!")


if __name__ == "__main__":
    main()
