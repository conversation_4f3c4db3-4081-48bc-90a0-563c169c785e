# O2C Interactive CLI Interface

## Overview

The O2C Interactive CLI provides step-by-step control over the complete Order-to-Cash workflow with user approval required for each stage. This gives you complete visibility and control over the entire process from email monitoring to invoice delivery.

## Features

- **Interactive Step-by-Step Execution** - User approval required for each stage
- **Detailed Output Display** - Clean, formatted results for each step
- **Colored Terminal Interface** - Easy-to-read status indicators and formatting
- **Complete Workflow Coverage** - All 7 stages from email to invoice
- **Error Handling** - Graceful handling of failures with clear error messages
- **Test PO Creation** - Built-in test PO generation for testing
- **Workflow Summary** - Complete summary at the end showing all results

## Quick Start

### Simple Command:
```bash
python3 run_o2c_cli.py
```

### Direct CLI:
```bash
python3 o2c_cli.py
```

## Workflow Stages

The CLI guides you through these 7 stages:

### Stage 0: Email Monitoring
- Monitors Gmail for new PO emails
- Allows selection of PO to process
- Option to create test PO if none found

### Stage 1: PO Parsing
- Extracts structured data from PO file
- Shows parsed customer, line items, pricing
- Displays delivery and payment terms

### Stage 2: Order Validation
- Comprehensive 7-layer validation
- Customer verification and credit checks
- Product catalog and business rules validation
- Risk assessment and recommendations

### Stage 3: Credit Assessment
- Customer creditworthiness evaluation
- Credit limit and available credit display
- Risk level assessment
- Approval conditions if applicable

### Stage 4: Inventory Assessment
- Product availability checking
- Stock level verification
- Fulfillment planning
- Lead time estimation

### Stage 5: Pricing Assessment
- Dynamic pricing calculation
- Contract terms application
- Discount and pricing rules
- Final pricing approval

### Stage 6: Fulfillment Coordination
- Warehouse optimization
- Shipping carrier selection
- Pick list generation
- Delivery tracking setup

### Stage 7: Invoice Generation
- Professional invoice creation
- Email distribution to customer
- Payment terms inclusion
- Delivery confirmation

## User Interface

### Color Coding:
- **Green (✓)** - Success messages and completed stages
- **Yellow (⚠)** - Warnings and user prompts
- **Red (✗)** - Errors and failures
- **Blue (ℹ)** - Information and stage headers
- **Cyan** - Data labels and field names

### User Controls:
- **y/yes** - Approve and continue to next stage
- **n/no** - Skip current stage (may terminate workflow)
- **q/quit** - Exit workflow immediately
- **Ctrl+C** - Emergency exit