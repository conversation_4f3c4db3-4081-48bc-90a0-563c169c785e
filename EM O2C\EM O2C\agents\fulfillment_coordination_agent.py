#!/usr/bin/env python3
"""
Fulfillment Coordination Agent for O2C System
Handles warehouse optimization, shipping coordination, delivery tracking, and fulfillment orchestration
"""

import os
import json
import sys
from datetime import datetime
from typing import Dict, Any, List
from crewai import Agent, Task, Crew, Process

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.llm.deepseek_llm import create_deepseek_llm_for_fulfillment
from agents.tools.fulfillment_coordination_tools import get_fulfillment_coordination_tools
from manage_verbosity import get_agent_config, get_crew_verbose
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FulfillmentCoordinationAgent:
    """
    Advanced Fulfillment Coordination Agent for comprehensive order fulfillment orchestration
    """
    
    def __init__(self):
        """Initialize the Fulfillment Coordination Agent"""
        try:
            self.llm = create_deepseek_llm_for_fulfillment()
            self.tools = get_fulfillment_coordination_tools()
            
            # Create the fulfillment agent
            self.fulfillment_agent = self._create_fulfillment_agent()
            
            # Create crew
            crew_verbose = get_crew_verbose()
            self.crew = Crew(
                agents=[self.fulfillment_agent],
                tasks=[],  # Tasks will be added dynamically
                process=Process.sequential,
                verbose=crew_verbose,
                memory=False  # Disabled to avoid OpenAI API dependency
            )
            
            logger.info(" Fulfillment Coordination Agent initialized successfully")
            
        except Exception as e:
            logger.error(f" Failed to initialize Fulfillment Coordination Agent: {str(e)}")
            raise
    
    def _create_fulfillment_agent(self) -> Agent:
        """Create the fulfillment coordination agent"""
        # Get verbosity setting from configuration
        agent_config = get_agent_config("fulfillment_coordination_agent")
        verbose = agent_config.get("verbose", False)
        allow_delegation = agent_config.get("allow_delegation", False)

        return Agent(
            role="Senior Fulfillment Coordination Specialist",
            goal="""Orchestrate optimal fulfillment operations by coordinating warehouse activities,
            shipping logistics, and delivery tracking to ensure efficient and cost-effective order fulfillment
            while maintaining high customer satisfaction and operational excellence.""",
            backstory="""You are an expert fulfillment coordinator with extensive experience in warehouse
            operations, logistics management, and supply chain optimization. You excel at coordinating complex
            fulfillment processes, optimizing shipping costs, and ensuring timely deliveries.

            Your expertise includes:
            - Warehouse optimization and allocation strategies
            - Multi-carrier shipping coordination and cost optimization
            - Pick list generation and work order management
            - Real-time delivery tracking and exception handling
            - Fulfillment process automation and efficiency improvement

            You work closely with warehouse teams, shipping carriers, and customer service to ensure
            seamless order fulfillment from pick to delivery. You are detail-oriented, proactive in
            identifying potential issues, and skilled at finding creative solutions to fulfillment challenges.""",
            verbose=verbose,
            allow_delegation=allow_delegation,
            llm=self.llm,
            tools=self.tools,
            max_iter=5,
            memory=False  # Disabled to avoid OpenAI API dependency
        )
    
    def create_fulfillment_coordination_task(self, parsed_order_data: Dict[str, Any], 
                                           inventory_result: Dict[str, Any], 
                                           pricing_result: Dict[str, Any]) -> Task:
        """Create a comprehensive fulfillment coordination task"""
        
        # Extract key information with safe access
        po_number = parsed_order_data.get("po_number", "UNKNOWN") if isinstance(parsed_order_data, dict) else "UNKNOWN"

        # Try different customer name locations
        customer_name = "UNKNOWN"
        if isinstance(parsed_order_data, dict):
            # Try customer_info.name first
            customer_info = parsed_order_data.get("customer_info", {})
            if isinstance(customer_info, dict):
                customer_name = customer_info.get("name", "UNKNOWN")

            # If not found, try customer.name
            if customer_name == "UNKNOWN":
                customer = parsed_order_data.get("customer", {})
                if isinstance(customer, dict):
                    customer_name = customer.get("name", "UNKNOWN")

        line_items = parsed_order_data.get("line_items", []) if isinstance(parsed_order_data, dict) else []
        delivery_details = parsed_order_data.get("delivery_details", {}) if isinstance(parsed_order_data, dict) else {}
        
        # Get inventory status with safe access
        inventory_status = "UNKNOWN"
        if isinstance(inventory_result, dict):
            inventory_assessment = inventory_result.get("inventory_assessment", {})
            if isinstance(inventory_assessment, dict):
                inventory_status = inventory_assessment.get("overall_fulfillment_status", "UNKNOWN")

        # Get pricing information with safe access
        pricing_total = 0
        if isinstance(pricing_result, dict):
            pricing_assessment = pricing_result.get("pricing_assessment", {})
            if isinstance(pricing_assessment, dict):
                pricing_calculations = pricing_assessment.get("pricing_calculations", {})
                if isinstance(pricing_calculations, dict):
                    final_pricing = pricing_calculations.get("final_pricing", {})
                    if isinstance(final_pricing, dict):
                        pricing_total = final_pricing.get("discounted_total", 0)
                    elif isinstance(final_pricing, (int, float)):
                        pricing_total = final_pricing
                elif isinstance(pricing_calculations, (int, float)):
                    pricing_total = pricing_calculations
            elif isinstance(pricing_assessment, (int, float)):
                pricing_total = pricing_assessment
        elif isinstance(pricing_result, (int, float)):
            pricing_total = pricing_result
        
        task_description = f"""
        Coordinate comprehensive fulfillment operations for order {po_number} from customer {customer_name}.
        
        ORDER DETAILS:
        - PO Number: {po_number}
        - Customer: {customer_name}
        - Line Items: {len(line_items)} products
        - Order Value: ${pricing_total:,.2f}
        - Delivery Address: {delivery_details.get('address', 'Not specified') if isinstance(delivery_details, dict) else 'Not specified'}
        - Requested Delivery: {delivery_details.get('requested_date', 'Not specified') if isinstance(delivery_details, dict) else 'Not specified'}
        - Inventory Status: {inventory_status}
        
        LINE ITEMS:
        {json.dumps(line_items, indent=2)}
        
        FULFILLMENT COORDINATION WORKFLOW:
        
        1. **WAREHOUSE OPTIMIZATION**
           - Use the warehouse_optimizer tool to determine optimal warehouse allocation
           - Consider delivery location, inventory availability, and processing capacity
           - Optimize for cost, speed, and efficiency
           - Generate comprehensive fulfillment plan
        
        2. **PICK LIST GENERATION**
           - Use the pick_list_generator tool to create optimized pick lists and work orders
           - Sequence picking for maximum efficiency
           - Generate work orders for picking, packing, and shipping
           - Include special handling instructions
        
        3. **SHIPPING CARRIER SELECTION**
           - Use the shipping_carrier_selector tool to choose optimal carrier and service level
           - Consider cost, transit time, and delivery requirements
           - Evaluate multiple carrier options
           - Select best option based on customer needs and cost optimization
        
        4. **SHIPPING LABEL CREATION**
           - Use the shipping_label_creator tool to generate shipping labels and documentation
           - Create all necessary shipping documentation
           - Generate tracking numbers and delivery confirmations
           - Schedule carrier pickup
        
        5. **DELIVERY TRACKING SETUP**
           - Use the delivery_tracker tool to establish tracking and monitoring
           - Set up real-time delivery tracking
           - Configure customer notifications
           - Establish delivery milestone monitoring
        
        6. **EXCEPTION HANDLING PREPARATION**
           - Use the fulfillment_exception_handler tool to prepare for potential issues
           - Identify potential fulfillment risks
           - Establish contingency plans
           - Set up proactive monitoring and alerts
        
        EXPECTED DELIVERABLES:
        - Complete fulfillment coordination plan
        - Optimized warehouse allocation and pick lists
        - Selected shipping carrier with cost optimization
        - Generated shipping labels and tracking information
        - Established delivery tracking and customer notifications
        - Exception handling procedures and contingency plans
        
        Focus on operational excellence, cost optimization, and customer satisfaction while ensuring 
        efficient and reliable order fulfillment.
        """
        
        return Task(
            description=task_description,
            agent=self.fulfillment_agent,
            expected_output="""A comprehensive fulfillment coordination report in JSON format containing:
            {
                "fulfillment_coordination": {
                    "po_number": "string",
                    "coordination_date": "ISO datetime",
                    "fulfillment_status": "COORDINATED|PENDING|EXCEPTION",
                    "warehouse_optimization": {...},
                    "pick_list_generation": {...},
                    "shipping_coordination": {...},
                    "delivery_tracking": {...},
                    "exception_handling": {...},
                    "fulfillment_timeline": {...},
                    "cost_optimization": {...},
                    "recommendations": [...],
                    "next_steps": "string"
                }
            }"""
        )
    
    def coordinate_fulfillment_for_order(self, parsed_order_data: Dict[str, Any], 
                                       inventory_result: Dict[str, Any], 
                                       pricing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Coordinate fulfillment operations for an order
        
        Args:
            parsed_order_data: Parsed order information
            inventory_result: Inventory assessment results
            pricing_result: Pricing assessment results
            
        Returns:
            Fulfillment coordination results
        """
        try:
            logger.info(f"🚚 Starting fulfillment coordination for PO: {parsed_order_data.get('po_number', 'UNKNOWN')}")
            
            # Create and execute fulfillment coordination task
            task = self.create_fulfillment_coordination_task(parsed_order_data, inventory_result, pricing_result)
            self.crew.tasks = [task]
            
            # Execute the crew
            result = self.crew.kickoff()

            # Parse the result - handle CrewOutput object
            if hasattr(result, 'raw'):
                # CrewAI returns CrewOutput object with .raw attribute
                result_text = str(result.raw)
            else:
                # Fallback for other result types
                result_text = str(result)

            # Try to parse as JSON
            try:
                # Look for JSON content in the result
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    result_json = json.loads(json_match.group())
                else:
                    # Create structured result if no JSON found
                    result_json = {
                        "fulfillment_coordination": {
                            "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                            "coordination_date": datetime.now().isoformat(),
                            "fulfillment_status": "COORDINATED",
                            "agent_response": result_text,
                            "processing_summary": "Fulfillment coordination completed successfully"
                        }
                    }
                
                logger.info(f" Fulfillment coordination completed for PO: {parsed_order_data.get('po_number', 'UNKNOWN')}")
                return result_json
                
            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ Could not parse fulfillment result as JSON: {str(e)}")
                # Return structured fallback
                return {
                    "fulfillment_coordination": {
                        "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                        "coordination_date": datetime.now().isoformat(),
                        "fulfillment_status": "COORDINATED",
                        "agent_response": result_text,
                        "processing_summary": "Fulfillment coordination completed with text response"
                    }
                }
                
        except Exception as e:
            logger.error(f" Fulfillment coordination failed: {str(e)}")
            return {
                "fulfillment_coordination": {
                    "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                    "coordination_date": datetime.now().isoformat(),
                    "fulfillment_status": "EXCEPTION",
                    "error": str(e),
                    "processing_summary": "Fulfillment coordination failed"
                }
            }


def run_fulfillment_coordination(parsed_order_data: Dict[str, Any], 
                               inventory_result: Dict[str, Any], 
                               pricing_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Standalone function to run fulfillment coordination
    
    Args:
        parsed_order_data: Parsed order information
        inventory_result: Inventory assessment results
        pricing_result: Pricing assessment results
        
    Returns:
        Fulfillment coordination results
    """
    try:
        agent = FulfillmentCoordinationAgent()
        return agent.coordinate_fulfillment_for_order(parsed_order_data, inventory_result, pricing_result)
    except Exception as e:
        logger.error(f" Standalone fulfillment coordination failed: {str(e)}")
        return {
            "fulfillment_coordination": {
                "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                "coordination_date": datetime.now().isoformat(),
                "fulfillment_status": "EXCEPTION",
                "error": str(e),
                "processing_summary": "Standalone fulfillment coordination failed"
            }
        }


if __name__ == "__main__":
    # Test the fulfillment coordination agent
    print("🧪 Testing Fulfillment Coordination Agent...")
    
    # Test data
    test_order = {
        "po_number": "PO-FULFILLMENT-TEST-001",
        "customer_info": {"name": "Test Customer Corp"},
        "line_items": [
            {"product_code": "WIDGET-A100", "quantity": 25, "unit_price": 125.0},
            {"product_code": "BOLT-M8-50", "quantity": 100, "unit_price": 2.5}
        ],
        "delivery_details": {
            "address": "123 Test St, Test City, CA 12345",
            "requested_date": "2025-07-25"
        }
    }
    
    test_inventory = {
        "inventory_assessment": {
            "overall_fulfillment_status": "FULL_FULFILLMENT"
        }
    }
    
    test_pricing = {
        "pricing_assessment": {
            "pricing_calculations": {
                "final_pricing": {"discounted_total": 3375.0}
            }
        }
    }
    
    # Run test
    try:
        result = run_fulfillment_coordination(test_order, test_inventory, test_pricing)
        print(" Fulfillment coordination test completed successfully!")
        print(json.dumps(result, indent=2))
    except Exception as e:
        print(f" Test failed: {str(e)}")
