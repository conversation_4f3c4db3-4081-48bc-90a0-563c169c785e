PS D:\PO\EM O2C\EM O2C> python run_o2c_cli.py
================================================================================
                          O2C INTERACTIVE CLI LAUNCHER
================================================================================

This will start the interactive Order-to-Cash workflow CLI.
You'll have control over each stage and can see detailed results.

Features:
• Step-by-step workflow execution
• User approval required for each stage
• Detailed output display for each step
• Clean, colored terminal interface
• Complete workflow from email to invoice

 Environment check passed

Start the O2C Interactive CLI? (y/n): y

 Starting O2C Interactive CLI...
================================================================================

WARNING:agents.tools.pricing_management_tools:Database not available, using mock data
 Workflow orchestrator successfully loaded - automated processing enabled

================================================================================
                            O2C INTERACTIVE WORKFLOW
================================================================================

Welcome to the Simple O2C Interactive CLI!
This will guide you through the complete Order-to-Cash workflow.
You will be asked to approve each stage before execution.

► Start the O2C workflow? (y/n/q): y

Stage 0: Email Monitoring
────────────────────────────────────────────────────────────
Checking for new purchase orders...
Note: Email monitoring will NOT auto-trigger the workflow in CLI mode.
You will have full control over each step.

Options:
1. Check email for new POs
2. Use existing PO file
3. Create test PO

Select option (1-3): 1

Checking email for new POs...
INFO:services.email_monitor:Starting email monitoring cycle...
INFO:services.email_monitor:Connecting to Gmail IMAP server...
INFO:services.email_monitor:Successfully connected to Gmail
INFO:services.email_monitor:Extracted text attachment: sample_po.txt
INFO:services.email_monitor:Found 1 potential PO emails
INFO:services.email_monitor:Processing email ID: 38
INFO:services.email_monitor:Extracted text attachment: sample_po.txt
INFO:services.email_monitor:Extracted PO content to: data/incoming_pos\PO_20250723_142134_38.txt
INFO:services.email_monitor:Saved email metadata to: data/processed_emails\EMAIL_20250723_142134_38.json
INFO:services.email_monitor:Saved email metadata to: data/processed_emails\EMAIL_20250723_142134_38.json
INFO:services.email_monitor: Automated workflow disabled in config. Run 'python3 o2c_crew.py' to process manually
INFO:services.email_monitor:Successfully processed email 38
INFO:services.email_monitor:Disconnected from Gmail
⚠ No new POs found in email

Available PO files:
  1. PO_20250723_142134_38.txt

Select PO (1-1): 1

Interactive Stage-by-Stage Workflow:
You will be asked to approve each stage before execution.

Stages:
1. PO Parsing - Extract structured data
2. Order Validation - Comprehensive validation
3. Credit Assessment - Customer credit check
4. Inventory Assessment - Stock availability
5. Pricing Assessment - Dynamic pricing
6. Fulfillment Coordination - Shipping setup
7. Invoice Generation - Create and email invoice

► Start stage-by-stage execution? (y/n/q): y

Stage 1: PO Parsing
────────────────────────────────────────────────────────────
This stage will extract structured data from the purchase order file.
The PO will be parsed to extract customer info, line items, pricing, etc.

► Run PO Parsing stage? (y/n/q): y

Parsing purchase order...
INFO:services.workflow_orchestrator:Successfully created parsed file: PARSED_20250723_142134_38.json
✓ PO parsing completed successfully!

Parsed PO Summary:
────────────────────────────────────────
PO Number: PO-VALID-2025-002
Customer: ABC Manufacturing Corp
Total Amount: $3,728.44
Line Items: 2 items

Stage 2: Order Validation
────────────────────────────────────────────────────────────
This stage will perform comprehensive validation of the parsed order.
Includes customer verification, product checks, and business rules.

► Run Order Validation stage? (y/n/q): y

Running order validation...
 Starting Comprehensive Order Validation...
============================================================

============================================================
 Order Validation Complete!
============================================================

Validation Summary:
────────────────────────────────────────
Status: SUCCESS
Validation Result: REVIEW

Validation Details:
  • - Overall Validation Status: REVIEW REQUIRED
  • - Available Credit: $37,500.00
  • - Validation Message: Customer 'ABC Manufacturing Corp' validated successfully
  • - Validation Message: All products validated successfully against catalog
  • - Validation Message: Business rules validation failed due to missing required field
  • - Delivery Address: 456 Industrial Blvd, Anytown, CA 90211 (Verified)
  • - Validation Message: Customer 'ABC Manufacturing Corp' validated successfully
  • - Validation Message: All products validated successfully against catalog
  • - Validation Message: Business rules validation failed due to missing required field
  • - Delivery Address: 456 Industrial Blvd, Anytown, CA 90211 (Verified)
  • - Issues Found: 0
  • - Validation Message: Delivery information validated successfully
  • - Issues Found: 0
  • - Validation Message: Order meets all compliance requirements
  • - Validation Performed By: Senior Order Validation Specialist
  • - Validation Timestamp: 2025-07-23T14:22:33.725588
  • - System: Enterprise Order Validation System v3.2

Risk Assessment:
  • - Overall Risk Score: 30 (LOW-MEDIUM)
Execution Time: 226.98 seconds
✓ Order validation completed: SUCCESS

Stage 3: Credit Assessment
────────────────────────────────────────────────────────────
This stage will assess customer creditworthiness and payment risk.
Credit limits, payment history, and risk factors will be evaluated.

► Run Credit Assessment stage? (y/n/q): y

Running credit assessment...
 Starting Credit Management & Risk Assessment...
============================================================
 Maximum iterations reached. Requesting final answer.
============================================================
 Credit Assessment Complete!
============================================================

Credit Assessment Summary:
────────────────────────────────────────
Status: SUCCESS

Credit Assessment Details:
   - Validation Status: Approved with Conditions
  • - Current Credit Limit: $50,000
  • - Available Credit: $37,500
  • - Risk Score: 20 (LOW Risk)
  • - Comprehensive Risk Score: 72/100 (LOW-MEDIUM)
  • **5. Credit Decision:**
  • - Payment Terms: Net 30 (with 2% discount for payment within 10 days)
  • - Credit Limit: Maintain at $50,000 (no increase recommended at this time)
Execution Time: 115.71 seconds
✓ Credit assessment completed successfully!

Stage 4: Inventory Assessment
────────────────────────────────────────────────────────────
This stage will check product availability and plan fulfillment.
Stock levels, lead times, and fulfillment options will be evaluated.

► Run Inventory Assessment stage? (y/n/q): y

Running inventory assessment...
 Maximum iterations reached. Requesting final answer.

Inventory Assessment Summary:
────────────────────────────────────────
Status: SUCCESS

Inventory Assessment Details:
Execution Time: 156.83 seconds
✓ Inventory assessment completed successfully!

Stage 5: Pricing Assessment
────────────────────────────────────────────────────────────
This stage will calculate dynamic pricing and apply contract terms.
Base pricing, discounts, and contract conditions will be evaluated.

► Run Pricing Assessment stage? (y/n/q): y  

Running pricing assessment...

Pricing Assessment Summary:
────────────────────────────────────────
Status: SUCCESS

Pricing Assessment Details:
Execution Time: 48.54 seconds
✓ Pricing assessment completed successfully!

Stage 6: Fulfillment Coordination
────────────────────────────────────────────────────────────
This stage will coordinate warehouse operations and shipping.
Pick lists, shipping carriers, and delivery tracking will be set up.

► Run Fulfillment Coordination stage? (y/n/q): y

Running fulfillment coordination...
 Maximum iterations reached. Requesting final answer.

Fulfillment Coordination Summary:
────────────────────────────────────────
Status: SUCCESS

Fulfillment Coordination Details:
Execution Time: 335.64 seconds
✓ Fulfillment coordination completed successfully!

Stage 7: Invoice Generation & Email Distribution
────────────────────────────────────────────────────────────
This stage will generate a professional invoice and email it to the customer.
Payment terms, contact information, and delivery confirmation will be included.

► Run Invoice Generation stage? (y/n/q): y

Running invoice generation...
ERROR:agents.tools.invoice_generation_tools: Error generating invoice: 'str' object has no attribute 'get'
ERROR:agents.tools.invoice_generation_tools: Error generating invoice: 'str' object has no attribute 'get'
 Maximum iterations reached. Requesting final answer.

Invoice Generation Summary:
────────────────────────────────────────
Status: SUCCESS

Invoice Generation Details:
Execution Time: 73.04 seconds
✓ Invoice generation completed successfully!

================================================================================
                        WORKFLOW COMPLETED SUCCESSFULLY!
================================================================================

All 7 stages completed successfully!
Complete Order-to-Cash workflow executed from PO to Invoice.

Order Summary:
PO Number: PO-VALID-2025-002
Customer: ABC Manufacturing Corp
Total Value: $3,728.44
Status: INVOICE SENT

Next Steps:
• Monitor delivery tracking
• Follow up on payment
• Archive completed order
PS D:\PO\EM O2C\EM O2C> 