#!/usr/bin/env python3
"""
Database Configuration for O2C System
Manages SQLite database configuration and initialization
"""

import json
import os
from pathlib import Path
from typing import Dict, Any


def get_database_config() -> Dict[str, Any]:
    """Get database configuration from config file"""
    try:
        config_path = Path(__file__).parent / "deepseek_config.json"
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
                return config.get("database", {})
        else:
            # Return default configuration
            return get_default_database_config()
            
    except Exception as e:
        print(f"Warning: Failed to load database config: {e}")
        return get_default_database_config()


def get_default_database_config() -> Dict[str, Any]:
    """Get default database configuration"""
    return {
        "type": "sqlite",
        "path": "database/o2c_master_data.db",
        "auto_create": True,
        "connection_timeout": 30,
        "enable_foreign_keys": True,
        "backup_enabled": True,
        "backup_interval_hours": 24,
        "backup_retention_days": 30
    }


def get_database_path() -> str:
    """Get database file path"""
    config = get_database_config()
    db_path = config.get("path", "database/o2c_master_data.db")
    
    # Ensure absolute path
    if not os.path.isabs(db_path):
        # Make relative to project root
        project_root = Path(__file__).parent.parent
        db_path = project_root / db_path
    
    return str(db_path)


def ensure_database_directory():
    """Ensure database directory exists"""
    db_path = get_database_path()
    db_dir = os.path.dirname(db_path)
    os.makedirs(db_dir, exist_ok=True)


def is_database_initialized() -> bool:
    """Check if database is initialized"""
    db_path = get_database_path()
    return os.path.exists(db_path) and os.path.getsize(db_path) > 0


def initialize_database_if_needed():
    """Initialize database if it doesn't exist"""
    config = get_database_config()
    
    if config.get("auto_create", True) and not is_database_initialized():
        print("🔧 Database not found. Initializing SQLite master data database...")
        
        try:
            # Import and run database setup
            from database.setup_database import O2CMasterDataSetup
            
            ensure_database_directory()
            setup = O2CMasterDataSetup(get_database_path())
            setup.setup_complete_database()
            
            print(" Database initialized successfully!")
            return True
            
        except Exception as e:
            print(f" Failed to initialize database: {e}")
            print("Please run: python database/setup_database.py")
            return False
    
    return True


def get_connection_string() -> str:
    """Get database connection string"""
    db_path = get_database_path()
    config = get_database_config()
    
    # SQLite connection string with options
    options = []
    
    if config.get("connection_timeout"):
        options.append(f"timeout={config['connection_timeout']}")
    
    if config.get("enable_foreign_keys", True):
        options.append("foreign_keys=ON")
    
    connection_string = f"sqlite:///{db_path}"
    if options:
        connection_string += "?" + "&".join(options)
    
    return connection_string


def validate_database_config() -> bool:
    """Validate database configuration"""
    config = get_database_config()
    
    # Check required fields
    required_fields = ["type", "path"]
    for field in required_fields:
        if field not in config:
            print(f" Missing required database config field: {field}")
            return False
    
    # Check database type
    if config["type"] != "sqlite":
        print(f" Unsupported database type: {config['type']}")
        return False
    
    # Check if database directory is writable
    db_path = get_database_path()
    db_dir = os.path.dirname(db_path)
    
    try:
        os.makedirs(db_dir, exist_ok=True)
        # Test write access
        test_file = os.path.join(db_dir, ".write_test")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
    except Exception as e:
        print(f" Database directory not writable: {db_dir} - {e}")
        return False
    
    return True


def show_database_info():
    """Show database configuration information"""
    config = get_database_config()
    db_path = get_database_path()
    
    print("📊 Database Configuration:")
    print("-" * 30)
    print(f"  Type: {config.get('type', 'sqlite')}")
    print(f"  Path: {db_path}")
    print(f"  Exists: {'Yes' if is_database_initialized() else 'No'}")
    print(f"  Auto-create: {'Yes' if config.get('auto_create', True) else 'No'}")
    print(f"  Foreign Keys: {'Enabled' if config.get('enable_foreign_keys', True) else 'Disabled'}")
    
    if is_database_initialized():
        try:
            from database.db_connection import get_master_data_db
            db = get_master_data_db()
            stats = db.get_database_stats()
            
            print("\n📈 Database Statistics:")
            print("-" * 30)
            for table, count in stats.items():
                table_display = table.replace('_', ' ').title()
                print(f"  {table_display}: {count} records")
                
        except Exception as e:
            print(f"  ⚠️ Could not load database stats: {e}")


if __name__ == "__main__":
    # Test database configuration
    print("🔧 Testing Database Configuration")
    print("=" * 40)
    
    if validate_database_config():
        print(" Database configuration is valid")
        show_database_info()
        
        if not is_database_initialized():
            print("\n🔄 Initializing database...")
            initialize_database_if_needed()
    else:
        print(" Database configuration is invalid")
