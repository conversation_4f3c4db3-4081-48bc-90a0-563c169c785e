#!/usr/bin/env python3
"""
Demo: Complete O2C Workflow with Inventory, Pricing & Fulfillment Management
Demonstrates the full workflow including inventory, pricing, and fulfillment coordination stages
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from services.workflow_orchestrator import O2CWorkflowOrchestrator


def create_sample_order():
    """Create a sample parsed order for testing"""
    sample_order = {
        "po_number": "PO-INVENTORY-DEMO-001",
        "date": "2024-07-10",
        "customer_info": {
            "customer_id": "CUST-001",
            "name": "ABC Manufacturing Corp",
            "contact": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "555-0123"
        },
        "line_items": [
            {
                "line_number": 1,
                "product_code": "WIDGET-A100",
                "description": "Premium Widget Assembly Type A",
                "quantity": 75,  # Should be available (450 available, 50 safety stock = 400 ATP)
                "unit_price": 125.00,
                "line_total": 9375.00
            },
            {
                "line_number": 2,
                "product_code": "BOLT-M8-50",
                "description": "Stainless Steel Bolts M8x50",
                "quantity": 1000,  # Should be available (1800 available, 200 safety stock = 1600 ATP)
                "unit_price": 2.50,
                "line_total": 2500.00
            },
            {
                "line_number": 3,
                "product_code": "GASKET-RUBBER-12",
                "description": "Rubber Gasket 12mm",
                "quantity": 50,  # Should require backorder (0 available, discontinued)
                "unit_price": 5.75,
                "line_total": 287.50
            }
        ],
        "delivery_details": {
            "address": "123 Main St, Anytown, CA 12345",
            "requested_date": "2024-07-20"
        },
        "payment_terms": "Net 30",
        "currency": "USD",
        "pricing": {
            "subtotal": 12162.50,
            "tax_amount": 1003.41,
            "shipping": 125.00,
            "total": 13290.91
        },
        "parsed_timestamp": datetime.now().isoformat()
    }
    
    return sample_order


def save_sample_order(order_data):
    """Save sample order to parsed_pos directory"""
    os.makedirs("data/parsed_pos", exist_ok=True)
    
    filename = f"PARSED_{order_data['po_number'].replace('-', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    filepath = os.path.join("data/parsed_pos", filename)
    
    with open(filepath, 'w') as f:
        json.dump(order_data, f, indent=2)
    
    print(f"📄 Sample order saved: {filepath}")
    return filename


def run_inventory_workflow_demo():
    """Run complete workflow demo with inventory management"""
    print(" Starting O2C Workflow Demo with Inventory Management")
    print("=" * 70)
    
    try:
        # Create and save sample order
        print("📝 Creating sample order...")
        sample_order = create_sample_order()
        parsed_filename = save_sample_order(sample_order)
        
        print(f"\n Sample Order Details:")
        print(f"  PO Number: {sample_order['po_number']}")
        print(f"  Customer: {sample_order['customer_info']['name']}")
        print(f"  Line Items: {len(sample_order['line_items'])}")
        for item in sample_order['line_items']:
            print(f"    - {item['product_code']}: {item['quantity']} units")
        
        # Initialize workflow orchestrator
        print(f"\n🔧 Initializing workflow orchestrator...")
        orchestrator = O2CWorkflowOrchestrator()
        
        # Run complete workflow
        print(f"\n Running complete O2C workflow...")
        print("=" * 50)
        
        # Simulate the workflow trigger (normally done by email monitor)
        po_file_path = os.path.join("data/incoming_pos", f"PO_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        os.makedirs("data/incoming_pos", exist_ok=True)
        
        # Create a simple PO file for the workflow
        with open(po_file_path, 'w') as f:
            f.write(f"""=== EMAIL METADATA ===
Email ID: DEMO-001
Timestamp: {datetime.now().isoformat()}
Sender: <EMAIL>
Subject: Purchase Order {sample_order['po_number']}
Date: {datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}

=== EMAIL BODY ===
Purchase Order: {sample_order['po_number']}
Customer: {sample_order['customer_info']['name']}

Line Items:
""")
            for item in sample_order['line_items']:
                f.write(f"- {item['product_code']}: {item['quantity']} x ${item['unit_price']:.2f}\n")
        
        # Run the workflow
        result = orchestrator.trigger_agent_workflow(po_file_path)
        
        # Display results
        print("\n" + "=" * 70)
        print("📊 WORKFLOW EXECUTION RESULTS")
        print("=" * 70)
        
        print(f"Workflow ID: {result.get('workflow_id', 'UNKNOWN')}")
        print(f"Overall Status: {result.get('overall_status', 'UNKNOWN')}")
        print(f"Started: {result.get('started_at', 'UNKNOWN')}")
        print(f"Completed: {result.get('completed_at', 'UNKNOWN')}")
        
        if result.get('errors'):
            print(f"Errors: {', '.join(result['errors'])}")
        
        # Show stage results
        stages = result.get('stages', {})
        
        print(f"\n📄 Stage 1 - PO Parsing:")
        po_stage = stages.get('po_parsing', {})
        print(f"  Status: {po_stage.get('status', 'UNKNOWN')}")
        if po_stage.get('parsed_file'):
            print(f"  Parsed File: {po_stage['parsed_file']}")
        
        print(f"\n Stage 2 - Order Validation:")
        validation_stage = stages.get('order_validation', {})
        print(f"  Status: {validation_stage.get('status', 'UNKNOWN')}")
        
        print(f"\n Stage 3 - Credit Assessment:")
        credit_stage = stages.get('credit_assessment', {})
        print(f"  Status: {credit_stage.get('status', 'UNKNOWN')}")
        if credit_stage.get('credit_decision'):
            decision = credit_stage['credit_decision']
            print(f"  Decision: {decision.get('decision', 'UNKNOWN')}")
            print(f"  Risk Level: {decision.get('risk_level', 'UNKNOWN')}")
        
        print(f"\n📦 Stage 4 - Inventory Assessment:")
        inventory_stage = stages.get('inventory_assessment', {})
        print(f"  Status: {inventory_stage.get('status', 'UNKNOWN')}")

        if inventory_stage.get('inventory_assessment'):
            inv_assessment = inventory_stage['inventory_assessment']
            print(f"  Fulfillment Status: {inv_assessment.get('overall_fulfillment_status', 'UNKNOWN')}")

            # Show detailed inventory results if available
            if 'inventory_assessment' in inv_assessment:
                inv_details = inv_assessment['inventory_assessment']

        print(f"\n💰 Stage 5 - Pricing Assessment:")
        pricing_stage = stages.get('pricing_assessment', {})
        print(f"  Status: {pricing_stage.get('status', 'UNKNOWN')}")

        if pricing_stage.get('pricing_assessment'):
            pricing_assessment = pricing_stage['pricing_assessment']

            # Show pricing summary if available
            if 'pricing_calculations' in pricing_assessment:
                pricing_calc = pricing_assessment['pricing_calculations']
                if 'final_pricing' in pricing_calc:
                    print(f"  Final Pricing: {pricing_calc['final_pricing']}")

            # Show approval status if available
            if 'approval_workflow' in pricing_assessment:
                approval = pricing_assessment['approval_workflow']
                print(f"  Approval Required: {approval.get('approval_required', 'Unknown')}")
                if approval.get('approval_required'):
                    print(f"  Approval Level: {approval.get('approval_level', 'Unknown')}")
                    print(f"  Timeline: {approval.get('approval_timeline', 'Unknown')}")
                print(f"  Assessment Date: {inv_details.get('assessment_date', 'UNKNOWN')}")
                
                # Show line item fulfillment
                if 'atp_calculations' in inv_details and 'line_items' in inv_details['atp_calculations']:
                    print(f"\n   Line Item Fulfillment:")
                    for item in inv_details['atp_calculations']['line_items']:
                        product = item.get('product_code', 'UNKNOWN')
                        requested = item.get('requested_quantity', 0)
                        allocated = item.get('allocated_quantity', 0)
                        backorder = item.get('backorder_quantity', 0)
                        status = item.get('fulfillment_status', 'UNKNOWN')
                        
                        print(f"    {product}: {allocated}/{requested} allocated, {backorder} backorder ({status})")

        print(f"\n🚚 Stage 6 - Fulfillment Coordination:")
        fulfillment_stage = stages.get('fulfillment_coordination', {})
        print(f"  Status: {fulfillment_stage.get('status', 'UNKNOWN')}")

        if fulfillment_stage.get('fulfillment_coordination'):
            fulfillment_coord = fulfillment_stage['fulfillment_coordination']
            print(f"  Fulfillment Status: {fulfillment_coord.get('fulfillment_status', 'UNKNOWN')}")

            # Show warehouse optimization if available
            if 'warehouse_optimization' in fulfillment_coord:
                warehouse = fulfillment_coord['warehouse_optimization']
                print(f"  Selected Warehouse: {warehouse.get('selected_warehouse', 'Unknown')}")

            # Show shipping coordination if available
            if 'shipping_coordination' in fulfillment_coord:
                shipping = fulfillment_coord['shipping_coordination']
                print(f"  Shipping Carrier: {shipping.get('selected_carrier', {}).get('carrier', 'Unknown')}")
                print(f"  Estimated Delivery: {shipping.get('estimated_delivery', 'Unknown')}")

        # Show next steps
        print(f"\n🎯 Next Steps:")
        if result.get('overall_status') == 'READY_FOR_PRICING':
            print("   Order ready for pricing and contract management")
        elif result.get('overall_status') == 'PARTIAL_FULFILLMENT_READY':
            print("  ⚠️ Partial fulfillment - proceed with available items")
        elif result.get('overall_status') == 'BACKORDER_SCHEDULED':
            print("  📅 Backorders scheduled - customer notification required")
        elif result.get('overall_status') == 'DENIED':
            print("   Order denied - customer notification required")
        else:
            print(f"  Status: {result.get('overall_status', 'UNKNOWN')}")
        
        print(f"\n🎉 Workflow demo completed successfully!")
        
        return result
        
    except Exception as e:
        print(f" Workflow demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = run_inventory_workflow_demo()
    
    if result:
        print(f"\n Demo completed successfully!")
        print(f"📊 Final Status: {result.get('overall_status', 'UNKNOWN')}")
    else:
        print(f"\n Demo failed!")
        sys.exit(1)
