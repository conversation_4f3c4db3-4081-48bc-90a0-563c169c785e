#!/usr/bin/env python3
"""
Suppress LLM and HTTP logging for clean O2C output
"""

import os
import logging
import warnings

def suppress_all_llm_logging():
    """Suppress all LLM-related logging and warnings"""
    
    # Set environment variables to suppress LiteLLM logging
    os.environ["LITELLM_LOG"] = "ERROR"
    os.environ["LITELLM_DROP_PARAMS"] = "true"
    os.environ["LITELLM_SUCCESS_CALLBACK"] = ""
    os.environ["LITELLM_FAILURE_CALLBACK"] = ""
    
    # Suppress specific loggers
    loggers_to_suppress = [
        "LiteLLM",
        "litellm", 
        "litellm.utils",
        "litellm.cost_calculator",
        "httpx",
        "httpcore",
        "opentelemetry",
        "opentelemetry.trace",
        "opentelemetry.instrumentation",
        "urllib3",
        "requests"
    ]
    
    for logger_name in loggers_to_suppress:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.ERROR)
        logger.disabled = True
    
    # Suppress warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", message=".*TracerProvider.*")
    warnings.filterwarnings("ignore", message=".*cost calculation.*")
    
    # Set root logging level to reduce noise
    logging.getLogger().setLevel(logging.WARNING)

def setup_clean_logging():
    """Setup clean logging for O2C operations"""
    
    # Suppress all LLM logging first
    suppress_all_llm_logging()
    
    # Configure clean logging format
    logging.basicConfig(
        level=logging.WARNING,
        format='%(levelname)s: %(message)s',
        force=True
    )
    
    # Only allow specific O2C loggers
    o2c_loggers = [
        "agents.po_parser_agent",
        "agents.order_validation_agent", 
        "agents.credit_management_agent",
        "agents.inventory_management_agent",
        "agents.pricing_management_agent",
        "agents.fulfillment_coordination_agent",
        "agents.invoice_generation_agent",
        "services.workflow_orchestrator",
        "services.email_monitor"
    ]
    
    for logger_name in o2c_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

# Apply suppression when module is imported
suppress_all_llm_logging()
