{"po_number": "PO-VALID-2025-002", "date": "2025-07-22", "reference_numbers": [], "vendor": {"name": "Test Supplier Inc.", "address": {"street": "789 Vendor Avenue", "city": "Supplier City", "state": "TX", "zip": "75001", "country": ""}, "contact": {"phone": "(*************", "email": "<EMAIL>", "person": "", "title": ""}}, "customer": {"name": "ABC Manufacturing Corp", "billing_address": {"street": "123 Business Street", "city": "Anytown", "state": "CA", "zip": "90210", "country": ""}, "shipping_address": {"street": "456 Industrial Blvd", "city": "Anytown", "state": "CA", "zip": "90211", "country": ""}, "contact": {"phone": "(*************", "email": "<EMAIL>", "person": "<PERSON>", "department": "Receiving Department"}}, "line_items": [{"item_code": "WIDGET-A100", "description": "Premium Widget Assembly Type A", "quantity": 25, "unit_of_measure": "units", "unit_price": 125.0, "line_total": 3125.0}, {"item_code": "BOLT-M8-50", "description": "M8 x 50mm Stainless Steel Bolts", "quantity": 100, "unit_of_measure": "pieces", "unit_price": 2.5, "line_total": 250.0}], "pricing": {"subtotal": 3375.0, "tax_rate": 8.25, "tax_amount": 278.44, "shipping": 75.0, "discount": 0.0, "total": 3728.44}, "delivery": {"address": "456 Industrial Blvd, Anytown, CA 90211", "requested_date": "2025-08-10", "shipping_method": "Standard Ground", "special_instructions": "Please deliver to loading dock B between 8:00 AM - 4:00 PM\nAll items must be properly packaged and labeled\nInclude packing slip with shipment\nNotify receiving department 24 hours before delivery\nContact <PERSON> at (************* ext. 205 for any questions"}, "terms": {"payment_terms": "Net 30 Days", "conditions": "All items subject to inspection upon receipt\nDefective items will be returned at vendor expense\nPayment will be made within 30 days of receipt and approval\nThis purchase order constitutes the entire agreement", "authorized_by": "<PERSON>"}, "parsing_metadata": {"parsed_at": "2025-07-23T13:57:53.238492", "original_file": "PO_20250723_135649_38.txt", "parser_version": "1.0", "workflow_stage": "parsed"}}