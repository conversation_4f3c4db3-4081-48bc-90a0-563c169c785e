#!/usr/bin/env python3
"""
Credit Management & Risk Assessment Agent
Advanced financial intelligence for O2C automation
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from crewai import Agent, Task, Crew, Process
from crewai.tools import tool
from agents.tools.credit_management_tools import get_credit_management_tools
from config.llm_config import get_llm_for_crewai
from manage_verbosity import get_agent_config, get_crew_verbose

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_credit_management_agent():
    """Create the Credit Management & Risk Assessment Agent"""

    # Get DeepSeek LLM configuration for CrewAI
    llm = get_llm_for_crewai()

    # Get verbosity setting from configuration
    agent_config = get_agent_config("credit_management_agent")
    verbose = agent_config.get("verbose", False)
    allow_delegation = agent_config.get("allow_delegation", False)

    return Agent(
        role="Senior Credit Risk Manager",
        goal="""Perform comprehensive credit assessment and risk management for customer orders.
        Evaluate creditworthiness, analyze payment history, assess financial risk,
        and make intelligent credit decisions to optimize cash flow while minimizing risk.""",

        backstory="""You are an experienced Credit Risk Manager with 15+ years in financial services
        and credit management. You have deep expertise in:

        - Credit scoring and risk assessment methodologies
        - Payment behavior analysis and predictive modeling
        - Account receivables management and collections
        - Financial ratio analysis and cash flow evaluation
        - Regulatory compliance (SOX, GAAP, Basel III)
        - Credit policy development and implementation

        You use advanced analytics and machine learning to make data-driven credit decisions
        that balance business growth with risk mitigation. Your decisions directly impact
        company cash flow, customer relationships, and overall financial health.""",

        tools=get_credit_management_tools(),
        llm=llm,
        verbose=verbose,
        allow_delegation=allow_delegation,
        memory=False,  # Disabled to avoid OpenAI API dependency
        max_iter=3
    )


def create_credit_assessment_task(order_data: Dict[str, Any], validation_results: Dict[str, Any]):
    """Create credit assessment task for the agent"""
    
    customer_name = order_data.get("customer", {}).get("name", "Unknown")
    order_total = order_data.get("pricing", {}).get("total", 0)
    po_number = order_data.get("po_number", "Unknown")
    
    return Task(
        description=f"""
        Perform comprehensive credit assessment and risk management for order {po_number}.
        
        CREDIT ASSESSMENT WORKFLOW:
        
        STEP 1: Customer Credit Profile Analysis
        - Use customer_credit_analyzer tool to evaluate customer: {customer_name}
        - Analyze credit history, payment patterns, and financial stability
        - Review current credit utilization and available credit limits
        - Assess customer's industry risk and market position
        
        STEP 2: Payment History & Behavior Analysis
        - Use payment_history_analyzer tool to examine payment patterns
        - Calculate average payment days, late payment frequency
        - Identify seasonal payment trends and cash flow patterns
        - Evaluate dispute history and resolution patterns
        
        STEP 3: Account Receivables Assessment
        - Use ar_aging_analyzer tool to review outstanding receivables
        - Calculate days sales outstanding (DSO) and aging buckets
        - Assess collection efficiency and write-off history
        - Evaluate current exposure and concentration risk
        
        STEP 4: Financial Risk Scoring
        - Use financial_risk_calculator tool to compute comprehensive risk score
        - Consider order size relative to customer's typical orders
        - Analyze order frequency and seasonal patterns
        - Evaluate market conditions and industry trends
        
        STEP 5: Credit Decision & Recommendations
        - Use credit_decision_engine tool to make final credit determination
        - Consider order total: ${order_total:,.2f}
        - Apply credit policies and risk tolerance guidelines
        - Generate specific recommendations for payment terms and conditions
        
        STEP 6: Dynamic Credit Limit Management
        - Use credit_limit_optimizer tool to adjust credit limits if needed
        - Consider current order impact on total exposure
        - Recommend credit line increases/decreases based on performance
        - Set appropriate credit monitoring triggers
        
        ORDER DETAILS:
        - PO Number: {po_number}
        - Customer: {customer_name}
        - Order Total: ${order_total:,.2f}
        - Validation Status: {validation_results.get('overall_status', 'Unknown')}
        
        EXPECTED OUTPUT:
        Generate a comprehensive credit assessment report with:
        - Credit approval/denial decision with clear rationale
        - Recommended payment terms and credit conditions
        - Risk score and risk level classification
        - Credit limit recommendations and monitoring requirements
        - Specific actions required (if any) for order processing
        - Complete audit trail of credit analysis performed
        
        DECISION CRITERIA:
        - APPROVE: Low risk, good credit history, within limits
        - APPROVE WITH CONDITIONS: Medium risk, requires special terms
        - MANUAL REVIEW: High risk or unusual circumstances
        - DENY: Unacceptable risk or credit policy violations
        
        Be thorough, analytical, and provide actionable financial insights.
        """,
        
        expected_output="""Comprehensive credit assessment report with clear approval/denial decision,
        risk analysis, payment terms recommendations, and specific next steps for order processing.""",
        
        agent=None  # Will be set when creating the crew
    )


def run_credit_assessment(order_data: Dict[str, Any], validation_results: Dict[str, Any]) -> str:
    """Run credit assessment for an order"""
    
    try:
        logger.info(" Starting Credit Management & Risk Assessment...")
        print(" Starting Credit Management & Risk Assessment...")
        print("=" * 60)
        
        # Create agent and task
        agent = create_credit_management_agent()
        task = create_credit_assessment_task(order_data, validation_results)
        task.agent = agent
        
        # Create and run crew
        crew_verbose = get_crew_verbose()
        crew = Crew(
            agents=[agent],
            tasks=[task],
            process=Process.sequential,
            verbose=crew_verbose,
            memory=False  # Disabled to avoid OpenAI API dependency
        )
        
        # Execute the credit assessment
        result = crew.kickoff()
        
        print("=" * 60)
        print(" Credit Assessment Complete!")
        print("=" * 60)
        
        return str(result)
        
    except Exception as e:
        error_msg = f" Credit assessment failed: {str(e)}"
        logger.error(error_msg)
        print(error_msg)
        return error_msg


if __name__ == "__main__":
    # Test the credit management agent
    if len(sys.argv) > 1:
        # Load order data from parsed file
        parsed_file = sys.argv[1]
        try:
            with open(f"data/parsed_pos/{parsed_file}", 'r') as f:
                order_data = json.load(f)
            
            # Mock validation results for testing
            validation_results = {
                "overall_status": "VALIDATION_FAILED",
                "risk_level": "HIGH",
                "customer_validation": {"status": "PASS"},
                "product_validation": {"status": "FAIL"}
            }
            
            print(f"🧪 Testing Credit Management Agent with: {parsed_file}")
            result = run_credit_assessment(order_data, validation_results)
            print(f"📊 Credit Assessment Result:\n{result}")
            
        except Exception as e:
            print(f" Error loading order data: {str(e)}")
    else:
        print("Usage: python3 agents/credit_management_agent.py <parsed_order_file>")
        print("Example: python3 agents/credit_management_agent.py PARSED_20250708_234745_25.json")
