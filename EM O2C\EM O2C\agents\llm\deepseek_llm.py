#!/usr/bin/env python3
"""
DeepSeek LLM Configuration for CrewAI
Simple configuration using CrewAI's built-in LLM class
"""

import os
from crewai import LLM
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


def create_deepseek_llm(
    api_key: Optional[str] = None,
    model: Optional[str] = None,
    temperature: Optional[float] = None,
    max_tokens: int = 4000,
    timeout: int = 120
) -> LLM:
    """Create DeepSeek LLM instance using CrewAI's LLM class"""

    # Get configuration from environment variables
    if api_key is None:
        api_key = os.getenv("DEEPSEEK_API_KEY")

    if model is None:
        model = os.getenv("DEEPSEEK_MODEL_NAME", "deepseek/deepseek-chat")

    if temperature is None:
        temperature = float(os.getenv("LLM_TEMPERATURE", "0.1"))

    if not api_key:
        raise ValueError(
            "DeepSeek API key is required. "
            "Set DEEPSEEK_API_KEY environment variable or pass api_key parameter."
        )

    # Set the API key in environment for CrewAI to use
    os.environ["DEEPSEEK_API_KEY"] = api_key

    # Create LLM with DeepSeek configuration
    llm = LLM(
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=timeout,
        api_key=api_key
    )

    return llm


def create_deepseek_llm_for_parsing() -> LLM:
    """Create optimized DeepSeek LLM for PO parsing tasks"""
    return create_deepseek_llm(
        max_tokens=4000,  # Enough for detailed PO parsing
        timeout=120       # 2 minutes timeout
        # Uses environment variables for model, temperature, and API key
    )


def create_deepseek_llm_for_inventory() -> LLM:
    """Create optimized DeepSeek LLM for inventory management tasks"""
    return create_deepseek_llm(
        temperature=0.2,  # Slightly higher for inventory decision-making
        max_tokens=5000,  # More tokens for complex inventory analysis
        timeout=150       # Longer timeout for ATP calculations
    )


def create_deepseek_llm_with_json() -> LLM:
    """Create DeepSeek LLM optimized for JSON output"""
    api_key = os.getenv("DEEPSEEK_API_KEY")

    if not api_key:
        raise ValueError("DeepSeek API key is required. Set DEEPSEEK_API_KEY environment variable.")

    # Set the API key in environment
    os.environ["DEEPSEEK_API_KEY"] = api_key

    # Create LLM with JSON response format
    llm = LLM(
        model="deepseek/deepseek-chat",
        temperature=0.1,
        max_tokens=4000,
        timeout=120,
        response_format={"type": "json"},  # Request JSON output
        api_key=api_key
    )

    return llm


def create_deepseek_llm_for_fulfillment(api_key: Optional[str] = None) -> LLM:
    """Create DeepSeek LLM instance optimized for fulfillment coordination tasks"""
    if not api_key:
        api_key = os.getenv("DEEPSEEK_API_KEY")

    if not api_key:
        raise ValueError("DeepSeek API key is required. Set DEEPSEEK_API_KEY environment variable.")

    # Set the API key in environment
    os.environ["DEEPSEEK_API_KEY"] = api_key

    # Create LLM optimized for fulfillment operations
    llm = LLM(
        model="deepseek/deepseek-chat",
        temperature=0.3,  # Lower temperature for operational decisions
        max_tokens=4000,
        timeout=120,
        api_key=api_key
    )

    return llm


def test_deepseek_connection(api_key: Optional[str] = None) -> bool:
    """Test DeepSeek API connection using CrewAI LLM"""
    try:
        llm = create_deepseek_llm(api_key=api_key)
        # Note: CrewAI LLM doesn't have a direct test method
        # This will be tested when actually using the agent
        print("✓ DeepSeek LLM configured successfully")
        return True
    except Exception as e:
        print(f"DeepSeek LLM configuration failed: {str(e)}")
        return False


if __name__ == "__main__":
    # Test the DeepSeek LLM configuration
    print("Testing DeepSeek LLM configuration...")

    if test_deepseek_connection():
        print("✓ DeepSeek LLM ready for use!")
        print("✓ Use create_deepseek_llm() to get LLM instance for agents")
    else:
        print("✗ DeepSeek LLM configuration failed!")
        print("Please check your DEEPSEEK_API_KEY environment variable.")
