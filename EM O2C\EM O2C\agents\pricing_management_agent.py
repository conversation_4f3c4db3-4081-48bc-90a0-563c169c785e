"""
Dynamic Pricing & Contract Management Agent for O2C System

This agent handles:
- Customer contract retrieval and management
- Dynamic pricing calculations with discounts
- Volume discount analysis and optimization
- Margin analysis and optimization
- Contract compliance verification
- Pricing approval workflows
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from crewai import Agent, Task, Crew, Process
from manage_verbosity import get_agent_config, get_crew_verbose

# Import pricing management tools
from agents.tools.pricing_management_tools import (
    contract_retriever,
    pricing_calculator,
    volume_discount_analyzer,
    margin_optimizer,
    contract_compliance_checker,
    pricing_approval_workflow
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# DeepSeek LLM configuration
try:
    from crewai import LLM
    llm = LLM(
        model="deepseek/deepseek-chat",
        api_key="***********************************",
        temperature=0.4
    )
except ImportError:
    logger.warning("CrewAI LLM not available, using default configuration")
    llm = None


class PricingManagementAgent:
    """Advanced Dynamic Pricing & Contract Management Agent"""
    
    def __init__(self):
        """Initialize the Pricing Management Agent"""
        self.agent = self.create_pricing_agent()
        self.crew = None
        
    def create_pricing_agent(self) -> Agent:
        """Create the pricing management specialist agent"""
        
        tools = [
            contract_retriever,
            pricing_calculator,
            volume_discount_analyzer,
            margin_optimizer,
            contract_compliance_checker,
            pricing_approval_workflow
        ]
        
        # Get verbosity setting from configuration
        agent_config = get_agent_config("pricing_management_agent")
        verbose = agent_config.get("verbose", False)
        allow_delegation = agent_config.get("allow_delegation", False)

        agent = Agent(
            role="Dynamic Pricing & Contract Management Specialist",
            goal="Optimize pricing strategies while ensuring contract compliance and margin targets",
            backstory="""You are an expert pricing analyst and contract management specialist with deep
            knowledge of dynamic pricing strategies, volume discounts, margin optimization, and contract
            compliance. You excel at analyzing customer contracts, calculating optimal pricing, and
            ensuring all pricing decisions align with business objectives and contractual obligations.

            Your expertise includes:
            - Contract terms interpretation and compliance verification
            - Dynamic pricing calculations with multiple discount tiers
            - Volume discount analysis and optimization recommendations
            - Margin analysis and profitability optimization
            - Pricing approval workflow management
            - Competitive pricing strategies and market analysis

            You always ensure pricing decisions are profitable, compliant, and competitive while
            maximizing customer satisfaction and business value.""",
            tools=tools,
            llm=llm,
            verbose=verbose,
            allow_delegation=allow_delegation,
            max_iter=3,
            memory=False
        )
        
        return agent
    
    def create_pricing_assessment_task(self, order_data: Dict[str, Any], 
                                     inventory_result: Dict[str, Any]) -> Task:
        """Create a comprehensive pricing assessment task"""
        
        # Extract key information
        po_number = order_data.get('po_number', 'UNKNOWN')
        customer_name = order_data.get('customer_info', {}).get('name', 'Unknown Customer')
        order_total = order_data.get('pricing', {}).get('total', 0.0)
        line_items = order_data.get('line_items', [])
        
        # Format line items for display
        items_summary = []
        for item in line_items[:3]:  # Show first 3 items
            items_summary.append(f"- {item.get('product_code', 'N/A')}: {item.get('quantity', 0)} units @ ${item.get('unit_price', 0):.2f}")
        
        if len(line_items) > 3:
            items_summary.append(f"... and {len(line_items) - 3} more items")
        
        items_text = "\n        ".join(items_summary)
        
        task_description = f"""
        Conduct comprehensive dynamic pricing and contract management analysis for the following order:
        
        ORDER INFORMATION:
        - PO Number: {po_number}
        - Customer: {customer_name}
        - Order Total: ${order_total:,.2f}
        - Line Items ({len(line_items)} total):
        {items_text}
        
        INVENTORY STATUS:
        - Inventory Assessment: {inventory_result.get('status', 'Unknown')}
        - Fulfillment Status: {inventory_result.get('inventory_assessment', {}).get('overall_fulfillment_status', 'Unknown')}
        
        REQUIRED ANALYSIS STEPS:
        
        STEP 1: Contract Retrieval & Analysis
        - Use contract_retriever tool to get customer contract terms and pricing agreements
        - Analyze contract type, discount tiers, payment terms, and special conditions
        - Identify any contract-specific pricing rules or volume commitments
        - Check contract validity and expiration dates
        
        STEP 2: Dynamic Pricing Calculation
        - Use pricing_calculator tool to calculate optimized pricing based on contract terms
        - Apply base discounts, volume tiers, and product-specific pricing rules
        - Calculate line-by-line pricing with all applicable discounts
        - Generate comprehensive pricing breakdown with discount explanations
        
        STEP 3: Volume Discount Analysis
        - Use volume_discount_analyzer tool to identify volume discount opportunities
        - Analyze current quantities against volume tier thresholds
        - Calculate potential savings from quantity adjustments
        - Provide recommendations for volume optimization
        
        STEP 4: Margin Optimization
        - Use margin_optimizer tool to ensure pricing meets margin targets
        - Analyze current margins against minimum and target thresholds
        - Recommend pricing adjustments to optimize profitability
        - Balance competitive pricing with margin requirements
        
        STEP 5: Contract Compliance Verification
        - Use contract_compliance_checker tool to verify all pricing complies with contract terms
        - Check minimum/maximum order values, currency requirements, and approval thresholds
        - Identify any compliance violations or warnings
        - Ensure all pricing decisions are within contractual bounds
        
        STEP 6: Pricing Approval Workflow
        - Use pricing_approval_workflow tool to determine if approvals are required
        - Assess order value, margin levels, and discount percentages against approval thresholds
        - Generate approval requirements and workflow steps
        - Provide timeline estimates for approval processes
        
        DELIVERABLES:
        Provide a comprehensive pricing assessment including:
        1. Contract analysis summary with key terms and conditions
        2. Detailed pricing calculations with all discounts applied
        3. Volume discount opportunities and recommendations
        4. Margin analysis with optimization suggestions
        5. Contract compliance verification results
        6. Approval workflow requirements and next steps
        7. Final pricing recommendation with business justification
        
        Focus on maximizing profitability while ensuring customer satisfaction and contract compliance.
        """
        
        task = Task(
            description=task_description,
            agent=self.agent,
            expected_output="""A comprehensive pricing assessment report in JSON format containing:
            {
                "pricing_assessment": {
                    "order_reference": "PO number",
                    "customer_analysis": {
                        "contract_summary": "Contract terms and conditions",
                        "pricing_tier": "Customer pricing tier",
                        "special_terms": "Any special pricing terms"
                    },
                    "pricing_calculations": {
                        "line_item_pricing": "Detailed pricing for each item",
                        "total_discounts": "Total discount amount and percentage",
                        "final_pricing": "Final order total with all adjustments"
                    },
                    "volume_analysis": {
                        "current_tiers": "Current volume discount tiers",
                        "optimization_opportunities": "Volume optimization recommendations",
                        "potential_savings": "Potential savings from volume adjustments"
                    },
                    "margin_analysis": {
                        "current_margins": "Current margin percentages",
                        "margin_optimization": "Margin improvement recommendations",
                        "profitability_assessment": "Overall profitability analysis"
                    },
                    "compliance_verification": {
                        "contract_compliance": "Contract compliance status",
                        "violations": "Any compliance violations",
                        "warnings": "Compliance warnings or concerns"
                    },
                    "approval_workflow": {
                        "approval_required": "Whether approval is needed",
                        "approval_level": "Required approval level",
                        "approval_timeline": "Expected approval timeline"
                    },
                    "final_recommendation": {
                        "recommended_pricing": "Final recommended pricing",
                        "business_justification": "Justification for pricing decisions",
                        "next_steps": "Required next steps"
                    }
                },
                "processing_metadata": {
                    "agent_type": "pricing_management",
                    "processing_timestamp": "ISO timestamp",
                    "status": "SUCCESS/ERROR"
                }
            }"""
        )
        
        return task
    
    def assess_pricing_for_order(self, order_data: Dict[str, Any], 
                               inventory_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive pricing assessment for an order
        
        Args:
            order_data: Parsed and validated order data
            inventory_result: Results from inventory management assessment
            
        Returns:
            Pricing assessment results
        """
        try:
            logger.info(f"💰 Starting pricing assessment for PO: {order_data.get('po_number', 'UNKNOWN')}")
            
            # Create and execute pricing assessment task
            task = self.create_pricing_assessment_task(order_data, inventory_result)
            crew_verbose = get_crew_verbose()
            self.crew = Crew(
                agents=[self.agent],
                tasks=[task],
                process=Process.sequential,
                verbose=crew_verbose,
                memory=False
            )
            
            # Execute the crew
            result = self.crew.kickoff()
            
            # Parse the result - handle CrewOutput object
            if hasattr(result, 'raw'):
                # CrewAI returns CrewOutput object with .raw attribute
                result_text = str(result.raw)
            else:
                # Fallback for other result types
                result_text = str(result)
            
            # Try to parse as JSON, otherwise use as raw text
            try:
                pricing_result = json.loads(result_text)
            except json.JSONDecodeError:
                pricing_result = {"raw_output": result_text}
            
            # Ensure pricing_result is a dictionary before updating
            if not isinstance(pricing_result, dict):
                pricing_result = {"raw_output": str(pricing_result)}
            
            # Add metadata
            pricing_result.update({
                "agent_type": "pricing_management",
                "processing_timestamp": datetime.now().isoformat(),
                "status": "SUCCESS"
            })
            
            logger.info(f" Pricing assessment completed for PO: {order_data.get('po_number', 'UNKNOWN')}")
            return pricing_result
            
        except Exception as e:
            logger.error(f" Pricing assessment failed: {str(e)}")
            return {
                "error": f"Pricing assessment failed: {str(e)}",
                "agent_type": "pricing_management",
                "processing_timestamp": datetime.now().isoformat(),
                "status": "ERROR"
            }


def run_pricing_assessment(order_data: Dict[str, Any], inventory_result: Dict[str, Any]) -> str:
    """Run pricing assessment for an order"""
    
    try:
        logger.info("💰 Starting Dynamic Pricing & Contract Management...")
        print("💰 Starting Dynamic Pricing & Contract Management...")
        print("=" * 60)
        
        # Create agent
        agent = PricingManagementAgent()
        
        # Execute the pricing assessment
        result = agent.assess_pricing_for_order(order_data, inventory_result)
        
        print("=" * 60)
        print(" Pricing Assessment Complete!")
        print("=" * 60)
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f" Pricing assessment failed: {str(e)}")
        error_result = {
            "error": f"Pricing assessment failed: {str(e)}",
            "agent_type": "pricing_management",
            "processing_timestamp": datetime.now().isoformat(),
            "status": "ERROR"
        }
        return json.dumps(error_result, indent=2)


if __name__ == "__main__":
    # Test the pricing management agent
    sample_order = {
        "po_number": "TEST-PRICING-001",
        "date": "2024-07-10",
        "customer_info": {
            "customer_id": "CUST-001",
            "name": "Acme Corporation",
            "contact": "John Smith",
            "email": "<EMAIL>",
            "phone": "555-0123"
        },
        "line_items": [
            {
                "line_number": 1,
                "product_code": "WIDGET-A100",
                "description": "Premium Widget A100",
                "quantity": 150,
                "unit_price": 125.0,
                "line_total": 18750.0
            },
            {
                "line_number": 2,
                "product_code": "BOLT-M8-50",
                "description": "M8x50 Bolt",
                "quantity": 500,
                "unit_price": 2.5,
                "line_total": 1250.0
            }
        ],
        "delivery_details": {
            "address": "123 Main St, Anytown, USA",
            "requested_date": "2024-07-25"
        },
        "payment_terms": "Net 30",
        "currency": "USD",
        "pricing": {
            "subtotal": 20000.0,
            "tax_amount": 1600.0,
            "shipping": 100.0,
            "total": 21700.0
        }
    }
    
    sample_inventory_result = {
        "status": "SUCCESS",
        "inventory_assessment": {
            "overall_fulfillment_status": "FULL_FULFILLMENT"
        }
    }
    
    result = run_pricing_assessment(sample_order, sample_inventory_result)
    print(result)
