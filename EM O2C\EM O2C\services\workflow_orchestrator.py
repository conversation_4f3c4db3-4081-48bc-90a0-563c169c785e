#!/usr/bin/env python3
"""
O2C Workflow Orchestrator
Automated workflow management for the complete O2C agent pipeline
"""

import os
import sys
import json
import time
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from o2c_crew import O2CCrew
from agents.order_validation_agent import run_order_validation
from agents.credit_management_agent import run_credit_assessment
from agents.inventory_management_agent import InventoryManagementAgent
from agents.pricing_management_agent import PricingManagementAgent
from agents.fulfillment_coordination_agent import FulfillmentCoordinationAgent
from agents.invoice_generation_agent import InvoiceGenerationAgent

# Load environment variables
load_dotenv()


class O2CWorkflowOrchestrator:
    """Orchestrates the complete O2C agent workflow"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """Setup logging for workflow orchestration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{log_dir}/workflow_orchestrator.log'),
                logging.StreamHandler()
            ]
        )
    
    def trigger_agent_workflow(self, po_file_path: str) -> Dict[str, Any]:
        """
        Trigger the complete agent workflow for a new PO file
        
        Args:
            po_file_path: Path to the extracted PO file
            
        Returns:
            Workflow execution results
        """
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.logger.info(f" Starting O2C workflow {workflow_id} for file: {po_file_path}")
        
        workflow_result = {
            "workflow_id": workflow_id,
            "po_file": po_file_path,
            "started_at": datetime.now().isoformat(),
            "stages": {},
            "overall_status": "IN_PROGRESS",
            "errors": []
        }
        
        try:
            # Stage 1: PO Parsing
            self.logger.info("📄 Stage 1: Running PO Parser Agent...")
            parsing_result = self._run_po_parser(po_file_path)
            workflow_result["stages"]["po_parsing"] = parsing_result
            
            if parsing_result["status"] != "SUCCESS":
                workflow_result["overall_status"] = "FAILED"
                workflow_result["errors"].append("PO parsing failed")
                return workflow_result
            
            # Stage 2: Order Validation
            self.logger.info(" Stage 2: Running Order Validation Agent...")
            parsed_file = parsing_result.get("parsed_file")
            if parsed_file:
                validation_result = self._run_order_validation(parsed_file)
                workflow_result["stages"]["order_validation"] = validation_result

                # Continue to credit assessment regardless of validation status
                # Credit agent will make final decision based on all factors

                # Stage 3: Credit Management & Risk Assessment
                self.logger.info(" Stage 3: Running Credit Management Agent...")
                credit_result = self._run_credit_assessment(parsed_file, validation_result)
                workflow_result["stages"]["credit_assessment"] = credit_result

                # Continue to inventory management if credit approved
                if credit_result.get("status") == "SUCCESS":
                    credit_decision = credit_result.get("credit_decision", {}).get("decision", "UNKNOWN")

                    if credit_decision in ["APPROVE", "APPROVE_WITH_CONDITIONS"]:
                        # Stage 4: Inventory Management & ATP
                        self.logger.info("📦 Stage 4: Running Inventory Management Agent...")
                        inventory_result = self._run_inventory_assessment(parsed_file, validation_result, credit_result)
                        workflow_result["stages"]["inventory_assessment"] = inventory_result

                        # Continue to pricing management if inventory is available
                        if inventory_result.get("status") == "SUCCESS":
                            inventory_status = inventory_result.get("inventory_assessment", {}).get("overall_fulfillment_status", "UNKNOWN")

                            if inventory_status in ["FULL_FULFILLMENT", "PARTIAL_FULFILLMENT"]:
                                # Stage 5: Dynamic Pricing & Contract Management
                                self.logger.info("💰 Stage 5: Running Pricing Management Agent...")
                                pricing_result = self._run_pricing_assessment(parsed_file, inventory_result)
                                workflow_result["stages"]["pricing_assessment"] = pricing_result

                                # Continue to fulfillment coordination if pricing is approved
                                if pricing_result.get("status") == "SUCCESS":
                                    pricing_approval = pricing_result.get("pricing_assessment", {}).get("approval_workflow", {}).get("approval_required", False)

                                    if not pricing_approval:
                                        # Stage 6: Fulfillment Coordination
                                        self.logger.info("🚚 Stage 6: Running Fulfillment Coordination Agent...")
                                        fulfillment_result = self._run_fulfillment_coordination(parsed_file, inventory_result, pricing_result)
                                        workflow_result["stages"]["fulfillment_coordination"] = fulfillment_result

                                        # Determine final workflow status based on fulfillment
                                        if fulfillment_result.get("status") == "SUCCESS":
                                            fulfillment_status = fulfillment_result.get("fulfillment_coordination", {}).get("fulfillment_status", "UNKNOWN")

                                            if fulfillment_status == "COORDINATED":
                                                # Stage 7: Invoice Generation & Email Distribution
                                                self.logger.info("📧 Stage 7: Running Invoice Generation Agent...")
                                                invoice_result = self._run_invoice_generation(parsed_file, fulfillment_result)
                                                workflow_result["stages"]["invoice_generation"] = invoice_result

                                                # Determine final workflow status based on invoice generation
                                                if invoice_result.get("status") == "SUCCESS":
                                                    workflow_result["overall_status"] = "INVOICE_SENT"
                                                else:
                                                    workflow_result["overall_status"] = "INVOICE_FAILED"
                                                    workflow_result["errors"].append("Invoice generation failed")
                                            elif fulfillment_status == "EXCEPTION":
                                                workflow_result["overall_status"] = "FULFILLMENT_EXCEPTION"
                                            else:
                                                workflow_result["overall_status"] = "FULFILLMENT_PENDING"
                                        else:
                                            workflow_result["overall_status"] = "FULFILLMENT_FAILED"
                                            workflow_result["errors"].append("Fulfillment coordination failed")
                                    else:
                                        workflow_result["overall_status"] = "PENDING_PRICING_APPROVAL"
                                else:
                                    workflow_result["overall_status"] = "PRICING_ASSESSMENT_FAILED"
                                    workflow_result["errors"].append("Pricing assessment failed")
                            elif inventory_status == "BACKORDER_REQUIRED":
                                workflow_result["overall_status"] = "BACKORDER_SCHEDULED"
                            else:
                                workflow_result["overall_status"] = "INVENTORY_ISSUES"
                        else:
                            workflow_result["overall_status"] = "INVENTORY_ASSESSMENT_FAILED"
                            workflow_result["errors"].append("Inventory assessment failed")

                    elif credit_decision == "MANUAL_REVIEW":
                        workflow_result["overall_status"] = "REQUIRES_MANUAL_REVIEW"
                    else:  # DENY
                        workflow_result["overall_status"] = "DENIED"
                else:
                    workflow_result["overall_status"] = "CREDIT_ASSESSMENT_FAILED"
                    workflow_result["errors"].append("Credit assessment failed")

            else:
                workflow_result["overall_status"] = "FAILED"
                workflow_result["errors"].append("No parsed file generated")
                return workflow_result

            # Workflow completed
            workflow_result["completed_at"] = datetime.now().isoformat()

            self.logger.info(f"🎉 Workflow {workflow_id} completed with status: {workflow_result['overall_status']}")

            # Save workflow results
            self._save_workflow_results(workflow_result)
            
            return workflow_result
            
        except Exception as e:
            self.logger.error(f" Workflow {workflow_id} failed: {str(e)}")
            workflow_result["overall_status"] = "ERROR"
            workflow_result["errors"].append(f"Workflow error: {str(e)}")
            workflow_result["completed_at"] = datetime.now().isoformat()
            return workflow_result
    
    def _run_po_parser(self, po_file_path: str) -> Dict[str, Any]:
        """Run the PO Parser Agent"""
        try:
            start_time = datetime.now()

            # Create O2C crew and run PO parsing
            o2c_crew = O2CCrew()

            # Extract filename for processing
            po_filename = os.path.basename(po_file_path)

            # Run the crew with the specific file
            result = o2c_crew.process_po_file(po_filename)

            # Check if parsing was successful by looking for parsed files
            parsed_files = self._find_parsed_files_after(start_time)

            # Determine success based on file creation (preferred) or agent result
            success = False
            status_message = ""

            if parsed_files:
                success = True
                status_message = f"Successfully created parsed file: {parsed_files[0]}"
                self.logger.info(status_message)
            elif isinstance(result, dict) and result.get("status") == "success":
                # Agent returned JSON directly - save it manually to continue workflow
                try:
                    json_data = result.get("result", "")
                    if json_data:
                        # Save the JSON data manually
                        saved_file = self._save_json_manually(json_data, po_file_path, start_time)
                        if saved_file:
                            success = True
                            status_message = f"Manually saved JSON data to: {saved_file}"
                            self.logger.info(f"Manually saved agent JSON to continue workflow: {saved_file}")
                            # Update parsed_files to include the manually saved file
                            parsed_files = [os.path.basename(saved_file)]
                        else:
                            success = False
                            status_message = "Failed to manually save JSON data"
                    else:
                        success = False
                        status_message = "No JSON data found in agent result"
                except Exception as e:
                    success = False
                    status_message = f"Error manually saving JSON: {str(e)}"
                    self.logger.error(f"Failed to manually save JSON: {str(e)}")
            else:
                status_message = "No parsed file created and no valid JSON returned"
                self.logger.error(status_message)

            return {
                "status": "SUCCESS" if success else "FAILED",
                "agent_result": str(result),
                "parsed_file": parsed_files[0] if parsed_files else None,
                "status_message": status_message,
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"PO Parser failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }
    
    def _run_order_validation(self, parsed_file: str) -> Dict[str, Any]:
        """Run the Order Validation Agent"""
        try:
            start_time = datetime.now()

            # Run order validation using the existing function
            result = run_order_validation(parsed_file)

            # Parse validation result to determine success
            validation_status = self._parse_validation_result(str(result))

            return {
                "status": "SUCCESS" if validation_status in ["PASS", "REVIEW"] else "FAILED",
                "validation_status": validation_status,
                "agent_result": str(result),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Order Validation failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _run_credit_assessment(self, parsed_file: str, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Run the Credit Management & Risk Assessment Agent"""
        try:
            start_time = datetime.now()

            # Load order data from parsed file
            parsed_file_path = f"data/parsed_pos/{parsed_file}"
            with open(parsed_file_path, 'r') as f:
                order_data = json.load(f)

            # Run credit assessment
            result = run_credit_assessment(order_data, validation_result)

            # Parse credit decision from result
            credit_decision = self._parse_credit_decision(str(result))

            return {
                "status": "SUCCESS",
                "credit_decision": credit_decision,
                "agent_result": str(result),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Credit Assessment failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _parse_credit_decision(self, result_text: str) -> Dict[str, Any]:
        """Parse credit assessment result to extract decision"""
        try:
            # Look for common decision patterns in the result
            if "APPROVE" in result_text and "CONDITIONS" in result_text:
                decision = "APPROVE_WITH_CONDITIONS"
            elif "APPROVE" in result_text:
                decision = "APPROVE"
            elif "MANUAL_REVIEW" in result_text or "REVIEW" in result_text:
                decision = "MANUAL_REVIEW"
            elif "DENY" in result_text or "REJECT" in result_text:
                decision = "DENY"
            else:
                decision = "UNKNOWN"

            # Extract risk level if available
            risk_level = "UNKNOWN"
            if "Risk Level: HIGH" in result_text or "HIGH RISK" in result_text:
                risk_level = "HIGH"
            elif "Risk Level: MEDIUM" in result_text or "MEDIUM RISK" in result_text:
                risk_level = "MEDIUM"
            elif "Risk Level: LOW" in result_text or "LOW RISK" in result_text:
                risk_level = "LOW"

            return {
                "decision": decision,
                "risk_level": risk_level,
                "summary": f"Credit decision: {decision} (Risk: {risk_level})"
            }

        except Exception as e:
            return {
                "decision": "ERROR",
                "risk_level": "UNKNOWN",
                "summary": f"Error parsing credit decision: {str(e)}"
            }

    def _run_inventory_assessment(self, parsed_file: str, validation_result: Dict[str, Any],
                                credit_result: Dict[str, Any]) -> Dict[str, Any]:
        """Run the Inventory Management Agent"""
        try:
            start_time = datetime.now()

            # Load order data from parsed file
            parsed_file_path = f"data/parsed_pos/{parsed_file}"
            with open(parsed_file_path, 'r') as f:
                order_data = json.load(f)

            # Initialize inventory agent
            inventory_agent = InventoryManagementAgent()

            # Run inventory assessment
            result = inventory_agent.assess_inventory_for_order(order_data, validation_result, credit_result)

            return {
                "status": "SUCCESS",
                "inventory_assessment": result.get("inventory_assessment", {}),
                "agent_result": result,
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Inventory Assessment failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "inventory_assessment": {
                    "overall_fulfillment_status": "ASSESSMENT_FAILED",
                    "error_details": str(e)
                },
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _run_pricing_assessment(self, parsed_file: str, inventory_result: Dict[str, Any]) -> Dict[str, Any]:
        """Run the Pricing Management Agent"""
        try:
            start_time = datetime.now()

            # Load order data from parsed file
            parsed_file_path = f"data/parsed_pos/{parsed_file}"
            with open(parsed_file_path, 'r') as f:
                order_data = json.load(f)

            # Initialize pricing agent
            pricing_agent = PricingManagementAgent()

            # Run pricing assessment
            result = pricing_agent.assess_pricing_for_order(order_data, inventory_result)

            return {
                "status": "SUCCESS",
                "pricing_assessment": result.get("pricing_assessment", {}),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat(),
                "summary": f"Pricing assessment completed for {order_data.get('po_number', 'UNKNOWN')}"
            }

        except Exception as e:
            self.logger.error(f"Pricing Assessment failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "pricing_assessment": {
                    "approval_workflow": {"approval_required": False},
                    "error_details": str(e)
                },
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _run_fulfillment_coordination(self, parsed_file: str, inventory_result: Dict[str, Any], pricing_result: Dict[str, Any]) -> Dict[str, Any]:
        """Run the Fulfillment Coordination Agent"""
        try:
            start_time = datetime.now()

            # Load order data from parsed file
            parsed_file_path = f"data/parsed_pos/{parsed_file}"
            with open(parsed_file_path, 'r') as f:
                order_data = json.load(f)

            # Initialize fulfillment agent
            fulfillment_agent = FulfillmentCoordinationAgent()

            # Run fulfillment coordination
            result = fulfillment_agent.coordinate_fulfillment_for_order(order_data, inventory_result, pricing_result)

            return {
                "status": "SUCCESS",
                "fulfillment_coordination": result.get("fulfillment_coordination", {}),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat(),
                "summary": f"Fulfillment coordination completed for {order_data.get('po_number', 'UNKNOWN')}"
            }

        except Exception as e:
            self.logger.error(f"Fulfillment Coordination failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "fulfillment_coordination": {
                    "fulfillment_status": "EXCEPTION",
                    "error_details": str(e)
                },
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _run_invoice_generation(self, parsed_file: str, fulfillment_result: Dict[str, Any]) -> Dict[str, Any]:
        """Run the Invoice Generation Agent"""
        try:
            start_time = datetime.now()

            # Load order data from parsed file
            parsed_file_path = f"data/parsed_pos/{parsed_file}"
            with open(parsed_file_path, 'r') as f:
                order_data = json.load(f)

            # Get DeepSeek API key
            deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
            if not deepseek_api_key:
                raise ValueError("DEEPSEEK_API_KEY not found in environment variables")

            # Initialize invoice generation agent
            invoice_agent = InvoiceGenerationAgent(deepseek_api_key)

            # Run invoice generation and email distribution
            result = invoice_agent.generate_and_send_invoice(order_data, fulfillment_result)

            return {
                "status": "SUCCESS",
                "invoice_generation": result.get("invoice_generation", {}),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "started_at": start_time.isoformat(),
                "completed_at": datetime.now().isoformat(),
                "summary": f"Invoice generated and sent for {order_data.get('po_number', 'UNKNOWN')}"
            }

        except Exception as e:
            self.logger.error(f"Invoice Generation failed: {str(e)}")
            return {
                "status": "ERROR",
                "error": str(e),
                "invoice_generation": {
                    "status": "ERROR",
                    "error_details": str(e)
                },
                "execution_time": 0,
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat()
            }

    def _find_parsed_files_after(self, start_time: datetime) -> List[str]:
        """Find parsed files created after the given start time"""
        parsed_dir = "data/parsed_pos"
        if not os.path.exists(parsed_dir):
            return []
        
        parsed_files = []
        for file in os.listdir(parsed_dir):
            if file.startswith("PARSED_") and file.endswith(".json"):
                file_path = os.path.join(parsed_dir, file)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                if file_time > start_time:
                    parsed_files.append(file)
        
        return sorted(parsed_files, reverse=True)  # Most recent first
    
    def _parse_validation_result(self, result_text: str) -> str:
        """Parse validation result to extract status"""
        # Convert to uppercase for case-insensitive matching
        result_upper = result_text.upper()

        if "OVERALL VALIDATION STATUS: PASS" in result_upper:
            return "PASS"
        elif "OVERALL VALIDATION STATUS: REVIEW" in result_upper:
            return "REVIEW"
        elif "OVERALL VALIDATION STATUS: FAIL" in result_upper:
            return "FAIL"
        else:
            return "UNKNOWN"
    
    def _save_json_manually(self, json_data: str, original_po_file: str, start_time: datetime) -> Optional[str]:
        """Manually save JSON data when agent bypasses po_data_saver tool"""
        try:
            # Parse JSON to validate it
            parsed_data = json.loads(json_data)

            # Create output directories
            parsed_dir = "data/parsed_pos"
            processed_dir = "data/processed_pos"
            os.makedirs(parsed_dir, exist_ok=True)
            os.makedirs(processed_dir, exist_ok=True)

            # Generate output filename
            timestamp = start_time.strftime("%Y%m%d_%H%M%S")
            original_filename = os.path.basename(original_po_file)
            base_name = original_filename.replace('.txt', '').replace('PO_', '')
            output_filename = f"PARSED_{base_name}.json"
            output_path = os.path.join(parsed_dir, output_filename)

            # Add metadata
            parsed_data["parsing_metadata"] = {
                "parsed_at": datetime.now().isoformat(),
                "original_file": original_filename,
                "parser_version": "1.0",
                "workflow_stage": "parsed",
                "saved_by": "workflow_orchestrator"
            }

            # Save to JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, indent=2, ensure_ascii=False)

            # Move original file to processed folder
            if os.path.exists(original_po_file):
                dest_path = os.path.join(processed_dir, original_filename)
                shutil.move(original_po_file, dest_path)
                self.logger.info(f"Moved original file: {original_po_file} → {dest_path}")

            return output_path

        except Exception as e:
            self.logger.error(f"Failed to manually save JSON: {str(e)}")
            return None

    def _save_workflow_results(self, workflow_result: Dict[str, Any]):
        """Save workflow results for audit and monitoring"""
        try:
            results_dir = "data/workflow_results"
            os.makedirs(results_dir, exist_ok=True)

            result_file = os.path.join(results_dir, f"{workflow_result['workflow_id']}.json")

            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_result, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📊 Workflow results saved to: {result_file}")

        except Exception as e:
            self.logger.error(f"Failed to save workflow results: {str(e)}")


def trigger_automated_workflow(po_file_path: str) -> Dict[str, Any]:
    """
    Entry point for triggering automated workflow
    
    Args:
        po_file_path: Path to the extracted PO file
        
    Returns:
        Workflow execution results
    """
    orchestrator = O2CWorkflowOrchestrator()
    return orchestrator.trigger_agent_workflow(po_file_path)


if __name__ == "__main__":
    # Test the orchestrator
    if len(sys.argv) > 1:
        po_file = sys.argv[1]
        print(f"🧪 Testing workflow orchestrator with file: {po_file}")
        result = trigger_automated_workflow(po_file)
        print(f"📊 Workflow Result: {json.dumps(result, indent=2)}")
    else:
        print("Usage: python3 services/workflow_orchestrator.py <po_file_path>")
        print("Example: python3 services/workflow_orchestrator.py data/incoming_pos/PO_20250708_220055_25.txt")
