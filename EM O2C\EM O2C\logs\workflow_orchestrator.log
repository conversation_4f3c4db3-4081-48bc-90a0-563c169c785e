2025-07-08 23:20:25,214 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_232025 for file: data/incoming_pos/PO_TEST_WORKFLOW_20250708_231921.txt
2025-07-08 23:20:25,214 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:20:25,237 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:20:25,395 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:20:25,399 - __main__ - ERROR - PO Parser failed: 'O2CCrew' object has no attribute 'run_po_parsing'
2025-07-08 23:23:11,233 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_232311 for file: data/incoming_pos/PO_TEST_WORKFLOW_20250708_231921.txt
2025-07-08 23:23:11,233 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:23:11,256 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:23:11,392 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:23:12,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:23:12,070 - root - ERROR - Error during short_term search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:23:12,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:23:12,856 - root - ERROR - Error during entities search: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:23:12,923 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:23:13,868 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:23:18,175 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:23:18,175 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:23:18,176 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:23:18,178 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:23:18,206 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:23:18,682 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:24:00,129 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:24:00,130 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:00,130 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:00,133 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:00,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:00,633 - root - ERROR - Error during short_term save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:02,536 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:24:02,819 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:24:23,096 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:24:23,097 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:23,097 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:23,106 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:24:23,557 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:23,559 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:24,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:24,116 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:24,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:24,559 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:25,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:25,038 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:25,445 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:25,447 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:25,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:25,960 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:24:26,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
2025-07-08 23:24:26,376 - root - ERROR - Error during entities save: APIStatusError.__init__() missing 2 required keyword-only arguments: 'response' and 'body'
2025-07-08 23:32:26,811 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_233226 for file: data/incoming_pos/PO_TEST_WORKFLOW_20250708_231921.txt
2025-07-08 23:32:26,811 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:32:26,815 - __main__ - ERROR - PO Parser failed: 1 validation error for Crew
  Value error, Please provide an OpenAI API key. You can get one at https://platform.openai.com/account/api-keys [type=value_error, input_value={'agents': [Agent(role=Se...': True, 'memory': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.9/v/value_error
2025-07-08 23:33:02,804 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_233302 for file: data/incoming_pos/PO_TEST_WORKFLOW_20250708_231921.txt
2025-07-08 23:33:02,805 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:33:02,820 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:33:03,424 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:33:10,174 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:33:10,175 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:10,175 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:10,177 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:10,264 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:33:10,747 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:33:16,209 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:33:16,209 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:16,209 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:16,211 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:16,215 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:33:16,468 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:33:23,172 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:33:23,173 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:23,173 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:23,176 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:33:23,181 - __main__ - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:33:23,181 - __main__ - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:34:03,509 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_233403 for file: data/incoming_pos/PO_LIVE_TEST_20250708_233341.txt
2025-07-08 23:34:03,509 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:34:03,525 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:34:04,267 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:34:08,841 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:34:08,841 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:08,842 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:08,843 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:08,924 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:34:09,256 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:34:42,543 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:34:42,547 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:42,547 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:42,554 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:34:42,562 - __main__ - INFO - Agent returned JSON directly (no file saved)
2025-07-08 23:34:42,563 - __main__ - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:38:43,334 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_233843 for file: data/incoming_pos/PO_FIXED_TEST_20250708_233651.txt
2025-07-08 23:38:43,335 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:38:43,349 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:38:43,835 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:38:48,366 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:38:48,367 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:48,370 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:48,370 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:48,467 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:38:48,757 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:38:54,232 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:38:54,233 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:54,233 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:54,234 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:38:54,238 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:38:54,494 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:39:01,011 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:39:01,014 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:01,014 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:01,017 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:01,023 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:39:01,367 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:39:34,510 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:39:34,513 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:34,513 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:34,517 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:39:34,523 - __main__ - WARNING - Agent bypassed po_data_saver tool - workflow may be incomplete
2025-07-08 23:39:34,524 - __main__ - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:41:13,289 - __main__ - INFO - 🚀 Starting O2C workflow workflow_20250708_234113 for file: data/incoming_pos/PO_FINAL_TEST_20250708_234142.txt
2025-07-08 23:41:13,289 - __main__ - INFO - 📄 Stage 1: Running PO Parser Agent...
2025-07-08 23:41:13,305 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:41:13,714 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:41:18,237 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:41:18,238 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:18,240 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:18,240 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:18,264 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:41:18,541 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:41:23,022 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:41:23,023 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:23,023 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:23,028 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:23,033 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:41:23,309 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:41:28,963 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:41:28,964 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:28,965 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:28,968 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:41:28,974 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:41:29,518 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:00,592 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:42:00,594 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:00,594 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:00,597 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:00,607 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:42:00,918 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:06,036 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:42:06,037 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:06,037 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:06,039 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:06,047 - __main__ - INFO - Successfully created parsed file: PARSED_FINAL_TEST_20250708_234104.json
2025-07-08 23:42:06,047 - __main__ - INFO - ✅ Stage 2: Running Order Validation Agent...
2025-07-08 23:42:06,051 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-08 23:42:06,058 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:42:06,346 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:11,095 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:42:11,096 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:11,096 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:11,101 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:11,107 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:42:11,362 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:16,501 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:42:16,501 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:16,501 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:16,507 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:16,508 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:42:16,795 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:30,197 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:42:30,199 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:30,211 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:42:30,216 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:42:30,605 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:04,195 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:43:04,196 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:04,197 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:04,258 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:04,259 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:43:04,709 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:13,666 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:43:13,667 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:13,673 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:13,682 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:43:14,227 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:48,229 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:43:48,230 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:48,231 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:48,283 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:43:48,285 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:43:48,736 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:44:29,494 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:44:29,496 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:44:29,496 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:44:29,580 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:44:29,581 - LiteLLM - INFO - 
LiteLLM completion() model= deepseek-chat; provider = deepseek
2025-07-08 23:44:30,013 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/beta/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:45:06,099 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-08 23:45:06,101 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:45:06,101 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
2025-07-08 23:45:06,173 - LiteLLM - INFO - selected model name for cost calculation: deepseek/deepseek-chat
