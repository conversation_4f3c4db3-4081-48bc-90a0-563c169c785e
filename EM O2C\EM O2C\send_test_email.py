#!/usr/bin/env python3
"""
Send Test Email for Live O2C Workflow Demo
"""

import smtplib
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta

def create_test_po_content(scenario_name: str) -> str:
    """Create test PO content for different scenarios"""
    
    today = datetime.now().strftime("%Y-%m-%d")
    future_date = (datetime.now() + timedelta(days=21)).strftime("%Y-%m-%d")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    scenarios = {
        "excellent_credit": {
            "customer": "Premium Manufacturing Inc",
            "po_number": f"PO-PREMIUM-{timestamp}",
            "address": "100 Excellence Blvd, Success City, CA 90210",
            "phone": "(*************",
            "email": "<EMAIL>",
            "authorized_by": "John Excellence",
            "items": [
                {"code": "WIDGET-A100", "desc": "Premium Widget Assembly", "qty": 50, "price": 125.50},
                {"code": "BOLT-M8-50", "desc": "M8x50mm Stainless Steel Bolt", "qty": 200, "price": 2.75}
            ]
        },
        "poor_credit": {
            "customer": "Struggling Enterprises LLC",
            "po_number": f"PO-STRUGGLE-{timestamp}",
            "address": "500 Difficulty Street, Challenge City, TX 75001",
            "phone": "(*************",
            "email": "<EMAIL>",
            "authorized_by": "Bob Struggle",
            "items": [
                {"code": "WIDGET-B200", "desc": "Standard Widget", "qty": 25, "price": 85.00},
                {"code": "CABLE-USB-3M", "desc": "USB Cable 3 Meter", "qty": 100, "price": 15.50}
            ]
        },
        "new_customer": {
            "customer": "Startup Innovations Corp",
            "po_number": f"PO-STARTUP-{timestamp}",
            "address": "300 Innovation Drive, Tech City, CA 94000",
            "phone": "(*************",
            "email": "<EMAIL>",
            "authorized_by": "Alice Innovator",
            "items": [
                {"code": "WIDGET-A100", "desc": "Premium Widget Assembly", "qty": 20, "price": 125.50}
            ]
        }
    }
    
    scenario = scenarios.get(scenario_name, scenarios["excellent_credit"])
    
    po_content = f"""PURCHASE ORDER - LIVE TEST

PO Number: {scenario['po_number']}
Date: {today}
Vendor: TestCorp Industries

BILL TO:
{scenario['customer']}
{scenario['address']}
Phone: {scenario['phone']}
Email: {scenario['email']}

SHIP TO:
{scenario['customer']} - Warehouse
{scenario['address'].replace('Blvd', 'Drive').replace('Street', 'Avenue')}
Attention: Receiving Department

VENDOR INFORMATION:
TestCorp Industries
789 Vendor Avenue
Supplier City, TX 75001
Phone: (*************
Email: <EMAIL>

ORDER DETAILS:
Requested Delivery Date: {future_date}
Payment Terms: Net 30
Shipping Method: Standard Ground

LINE ITEMS:"""
    
    total_amount = 0
    for i, item in enumerate(scenario['items'], 1):
        line_total = item['qty'] * item['price']
        total_amount += line_total
        po_content += f"""
{i}. Item Code: {item['code']}
   Description: {item['desc']}
   Quantity: {item['qty']} units
   Unit Price: ${item['price']:.2f}
   Line Total: ${line_total:.2f}"""
    
    # Calculate pricing
    subtotal = total_amount
    tax_amount = subtotal * 0.0825
    shipping = 125.00
    total = subtotal + tax_amount + shipping
    
    po_content += f"""

PRICING SUMMARY:
Subtotal: ${subtotal:.2f}
Tax (8.25%): ${tax_amount:.2f}
Shipping & Handling: ${shipping:.2f}
TOTAL AMOUNT: ${total:.2f}

SPECIAL INSTRUCTIONS:
- Please deliver to loading dock B between 8:00 AM - 4:00 PM
- All items must be properly packaged and labeled
- Include packing slip with shipment
- Notify receiving department 24 hours before delivery

TERMS AND CONDITIONS:
- All items subject to inspection upon receipt
- Defective items will be returned at vendor expense
- Payment will be made within 30 days of receipt and approval
- This purchase order constitutes the entire agreement

Authorized By: {scenario['authorized_by']}
Title: Procurement Manager
Date: {today}

Purchase Order Number: {scenario['po_number']}
Please reference this PO number on all correspondence and invoices.

--- LIVE O2C WORKFLOW TEST ---
Scenario: {scenario_name.replace('_', ' ').title()}
Expected Decision: {get_expected_decision(scenario_name)}
Test Timestamp: {datetime.now().isoformat()}
"""
    
    return po_content

def get_expected_decision(scenario_name: str) -> str:
    """Get expected decision for each scenario"""
    decisions = {
        "excellent_credit": "APPROVE - Immediate processing",
        "poor_credit": "DENY or MANUAL_REVIEW - High risk",
        "new_customer": "APPROVE_WITH_CONDITIONS - Credit application required"
    }
    return decisions.get(scenario_name, "Unknown")

def send_test_email(scenario_name: str, po_content: str):
    """Send test email with PO content"""
    
    # Email configuration
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    sender_email = "<EMAIL>"
    sender_password = "cmkd sqdx prqn yorm"  # App password
    recipient_email = "<EMAIL>"
    
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = f"LIVE TEST PO - {scenario_name.replace('_', ' ').title()} - {datetime.now().strftime('%H:%M:%S')}"
        
        # Email body
        body = f"""Dear TestCorp Industries,

🧪 LIVE O2C WORKFLOW TEST - {scenario_name.replace('_', ' ').title()}

Please find our purchase order for immediate processing.

This is a live test of the automated Order-to-Cash workflow system.
The system should automatically:
1. Detect this email
2. Extract the PO content
3. Parse the order data with AI
4. Validate the order
5. Perform credit assessment
6. Make an intelligent business decision

Expected outcome: {get_expected_decision(scenario_name)}

Best regards,
Automated Testing System

---
PURCHASE ORDER DETAILS:
{po_content}
"""
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, recipient_email, text)
        server.quit()
        
        print(f" Test email sent successfully for scenario: {scenario_name}")
        return True
        
    except Exception as e:
        print(f" Failed to send test email for {scenario_name}: {str(e)}")
        return False

def main():
    """Send test emails for live workflow demonstration"""
    
    print(" LIVE O2C WORKFLOW EMAIL TEST")
    print("=" * 60)
    print("Sending test emails to trigger automated workflow...")
    print("=" * 60)
    
    scenarios = ["excellent_credit", "poor_credit", "new_customer"]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📧 Sending Test {i}/3: {scenario.replace('_', ' ').title()}")
        print("-" * 40)
        
        # Create PO content
        po_content = create_test_po_content(scenario)
        
        # Send test email
        success = send_test_email(scenario, po_content)
        
        if success:
            print(f"   Expected Outcome: {get_expected_decision(scenario)}")
            print(f"   ⏱️ Email sent - workflow should trigger automatically")
        
        # Wait between emails
        if i < len(scenarios):
            print(f"   ⏳ Waiting 45 seconds before next test...")
            time.sleep(45)
    
    print(f"\n🎯 LIVE EMAIL TEST COMPLETE!")
    print(f"   All test emails sent successfully")
    print(f"   Monitor the email monitoring service to see real-time processing")
    print(f"   Each scenario will demonstrate different decision outcomes")

if __name__ == "__main__":
    main()
