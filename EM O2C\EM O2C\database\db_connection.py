#!/usr/bin/env python3
"""
SQLite Database Connection and Data Access Layer
Provides connection utilities and data access methods for O2C master data
"""

import sqlite3
import json
import os
from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager
from pathlib import Path


class O2CMasterDataDB:
    """O2C Master Data Database Access Layer"""
    
    def __init__(self, db_path: str = "database/o2c_master_data.db"):
        """Initialize database connection"""
        self.db_path = db_path
        
        # Ensure database exists
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"Database not found: {db_path}. Run setup_database.py first.")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with proper cleanup"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        try:
            yield conn
        finally:
            conn.close()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """Execute query and return results as list of dictionaries"""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def execute_single(self, query: str, params: tuple = ()) -> Optional[Dict]:
        """Execute query and return single result as dictionary"""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute update/insert/delete query and return affected rows"""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    # =====================================================
    # CUSTOMER DATA ACCESS METHODS
    # =====================================================
    
    def get_customer_by_name(self, customer_name: str) -> Optional[Dict]:
        """Get customer by name"""
        query = """
            SELECT * FROM customers 
            WHERE name = ? COLLATE NOCASE
        """
        return self.execute_single(query, (customer_name.strip(),))
    
    def get_customer_by_id(self, customer_id: str) -> Optional[Dict]:
        """Get customer by ID"""
        query = "SELECT * FROM customers WHERE customer_id = ?"
        return self.execute_single(query, (customer_id,))
    
    def get_all_customers(self, status: str = None) -> List[Dict]:
        """Get all customers, optionally filtered by status"""
        if status:
            query = "SELECT * FROM customers WHERE status = ? ORDER BY name"
            return self.execute_query(query, (status,))
        else:
            query = "SELECT * FROM customers ORDER BY name"
            return self.execute_query(query)
    
    def get_customer_addresses(self, customer_id: str) -> List[Dict]:
        """Get all addresses for a customer"""
        query = """
            SELECT * FROM customer_addresses 
            WHERE customer_id = ? 
            ORDER BY is_primary DESC, address_type
        """
        return self.execute_query(query, (customer_id,))
    
    def get_customer_with_addresses(self, customer_name: str) -> Optional[Dict]:
        """Get customer with all addresses"""
        customer = self.get_customer_by_name(customer_name)
        if customer:
            addresses = self.get_customer_addresses(customer['customer_id'])
            customer['addresses'] = addresses
        return customer
    
    # =====================================================
    # PRODUCT DATA ACCESS METHODS
    # =====================================================
    
    def get_product_by_code(self, product_code: str) -> Optional[Dict]:
        """Get product by code"""
        query = "SELECT * FROM products WHERE product_code = ?"
        return self.execute_single(query, (product_code,))
    
    def get_products_by_category(self, category: str) -> List[Dict]:
        """Get products by category"""
        query = "SELECT * FROM products WHERE category = ? ORDER BY description"
        return self.execute_query(query, (category,))
    
    def get_all_products(self, status: str = None) -> List[Dict]:
        """Get all products, optionally filtered by status"""
        if status:
            query = "SELECT * FROM products WHERE status = ? ORDER BY category, description"
            return self.execute_query(query, (status,))
        else:
            query = "SELECT * FROM products ORDER BY category, description"
            return self.execute_query(query)
    
    def search_products(self, search_term: str) -> List[Dict]:
        """Search products by description or code"""
        query = """
            SELECT * FROM products 
            WHERE product_code LIKE ? OR description LIKE ?
            ORDER BY description
        """
        search_pattern = f"%{search_term}%"
        return self.execute_query(query, (search_pattern, search_pattern))
    
    # =====================================================
    # BUSINESS RULES DATA ACCESS METHODS
    # =====================================================
    
    def get_business_rule(self, rule_name: str) -> Optional[Dict]:
        """Get business rule by name"""
        query = "SELECT * FROM business_rules WHERE rule_name = ? AND is_active = 1"
        return self.execute_single(query, (rule_name,))
    
    def get_business_rules_by_category(self, category: str) -> List[Dict]:
        """Get business rules by category"""
        query = """
            SELECT * FROM business_rules 
            WHERE rule_category = ? AND is_active = 1 
            ORDER BY rule_name
        """
        return self.execute_query(query, (category,))
    
    def get_all_business_rules(self) -> Dict[str, Any]:
        """Get all business rules as a dictionary (compatible with current mock format)"""
        query = "SELECT * FROM business_rules WHERE is_active = 1"
        rules = self.execute_query(query)
        
        result = {}
        for rule in rules:
            rule_value = rule['rule_value']
            
            # Parse JSON values
            if rule['rule_type'] == 'JSON':
                try:
                    rule_value = json.loads(rule_value)
                except json.JSONDecodeError:
                    pass
            # Convert numeric values
            elif rule['rule_type'] == 'NUMERIC':
                try:
                    rule_value = float(rule_value)
                    if rule_value.is_integer():
                        rule_value = int(rule_value)
                except ValueError:
                    pass
            # Convert boolean values
            elif rule['rule_type'] == 'BOOLEAN':
                rule_value = rule_value.lower() in ('true', '1', 'yes')
            
            result[rule['rule_name']] = rule_value
        
        return result
    
    # =====================================================
    # TAX RATES DATA ACCESS METHODS
    # =====================================================
    
    def get_tax_rate(self, state_code: str) -> Optional[float]:
        """Get tax rate for a state"""
        query = """
            SELECT tax_rate FROM tax_rates 
            WHERE state_code = ? AND is_active = 1
            ORDER BY effective_date DESC
            LIMIT 1
        """
        result = self.execute_single(query, (state_code,))
        return result['tax_rate'] if result else None
    
    def get_all_tax_rates(self) -> Dict[str, float]:
        """Get all tax rates as a dictionary (compatible with current mock format)"""
        query = """
            SELECT state_code, tax_rate FROM tax_rates 
            WHERE is_active = 1
            ORDER BY state_code
        """
        rates = self.execute_query(query)
        return {rate['state_code']: rate['tax_rate'] for rate in rates}
    
    # =====================================================
    # PAYMENT HISTORY DATA ACCESS METHODS
    # =====================================================
    
    def get_customer_payment_history(self, customer_id: str, limit: int = 50) -> List[Dict]:
        """Get payment history for a customer"""
        query = """
            SELECT * FROM payment_history 
            WHERE customer_id = ? 
            ORDER BY payment_date DESC 
            LIMIT ?
        """
        return self.execute_query(query, (customer_id, limit))
    
    def add_payment_record(self, customer_id: str, order_number: str, 
                          amount: float, days_to_pay: int, 
                          payment_method: str = 'CHECK', 
                          status: str = 'ON_TIME') -> int:
        """Add a payment record"""
        query = """
            INSERT INTO payment_history 
            (customer_id, order_number, payment_date, amount, days_to_pay, payment_method, status)
            VALUES (?, ?, datetime('now'), ?, ?, ?, ?)
        """
        return self.execute_update(query, (
            customer_id, order_number, amount, days_to_pay, payment_method, status
        ))
    
    # =====================================================
    # UTILITY METHODS
    # =====================================================
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        tables = ['customers', 'customer_addresses', 'products', 'business_rules', 'tax_rates', 'payment_history']
        stats = {}
        
        for table in tables:
            query = f"SELECT COUNT(*) as count FROM {table}"
            result = self.execute_single(query)
            stats[table] = result['count'] if result else 0
        
        return stats
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                conn.execute("SELECT 1")
                return True
        except Exception:
            return False


# Global database instance
_db_instance = None

def get_master_data_db() -> O2CMasterDataDB:
    """Get global database instance (singleton pattern)"""
    global _db_instance
    if _db_instance is None:
        _db_instance = O2CMasterDataDB()
    return _db_instance
