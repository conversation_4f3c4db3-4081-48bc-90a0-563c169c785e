#!/usr/bin/env python3
"""
Invoice Generation & Email Distribution Agent
Handles invoice creation and automated email distribution to customers
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from crewai import Agent, Task, Crew, Process
from agents.llm.deepseek_llm import create_deepseek_llm_for_parsing
from agents.tools.invoice_generation_tools import get_invoice_generation_tools
from manage_verbosity import get_agent_config, get_crew_verbose

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class InvoiceGenerationAgent:
    """Agent responsible for invoice generation and email distribution"""
    
    def __init__(self, deepseek_api_key: str):
        """Initialize the Invoice Generation Agent"""
        self.deepseek_api_key = deepseek_api_key
        # Set the API key in environment for the LLM
        import os
        os.environ["DEEPSEEK_API_KEY"] = deepseek_api_key
        self.llm = create_deepseek_llm_for_parsing()
        self.tools = get_invoice_generation_tools()
        
        # Get verbosity setting from configuration
        agent_config = get_agent_config("invoice_generation_agent")
        verbose = agent_config.get("verbose", False)
        allow_delegation = agent_config.get("allow_delegation", False)

        # Create the agent
        self.agent = Agent(
            role="Senior Invoice Generation & Distribution Specialist",
            goal="Generate professional invoices and distribute them to customers via email with accuracy and compliance",
            backstory="""You are an expert in invoice generation and customer billing operations.
            You have extensive experience in creating compliant invoices, calculating taxes,
            and managing customer communications. You ensure all invoices are accurate,
            professional, and delivered promptly to customers.""",
            tools=self.tools,
            llm=self.llm,
            verbose=verbose,
            allow_delegation=allow_delegation,
            max_iter=3
        )

        # Create crew
        crew_verbose = get_crew_verbose()
        self.crew = Crew(
            agents=[self.agent],
            tasks=[],  # Tasks will be added dynamically
            process=Process.sequential,
            verbose=crew_verbose
        )
        
        logger.info(" Invoice Generation Agent initialized successfully")
    
    def create_invoice_generation_task(self, parsed_order_data: Dict[str, Any], 
                                     fulfillment_result: Dict[str, Any]) -> Task:
        """Create invoice generation and distribution task"""
        
        # Extract key information
        po_number = parsed_order_data.get("po_number", "UNKNOWN")
        customer_name = parsed_order_data.get("customer", {}).get("name", "UNKNOWN")
        customer_email = parsed_order_data.get("customer", {}).get("contact", {}).get("email", "")
        total_amount = parsed_order_data.get("pricing", {}).get("total", 0)
        
        # Get fulfillment status
        fulfillment_status = fulfillment_result.get("fulfillment_coordination", {}).get("fulfillment_status", "UNKNOWN")
        
        task_description = f"""
        Generate and distribute professional invoice for completed order {po_number} from customer {customer_name}.
        
        ORDER DETAILS:
        - PO Number: {po_number}
        - Customer: {customer_name}
        - Customer Email: {customer_email}
        - Order Total: ${total_amount:,.2f}
        - Fulfillment Status: {fulfillment_status}
        
        INVOICE GENERATION & DISTRIBUTION WORKFLOW:
        
        STEP 1: Generate Professional Invoice
        - Use the invoice_generator tool to create a comprehensive TXT format invoice
        - Include all order details, line items, pricing, and shipping information
        - Calculate taxes and final amounts accurately
        - Generate unique invoice number and set payment terms
        - Ensure professional formatting and compliance
        
        STEP 2: Email Distribution (MANDATORY)
        - MUST use the invoice_email_sender tool to send the invoice to the customer
        - Pass the complete invoice data from Step 1 to the email sender tool
        - Verify email delivery confirmation is received
        - Do NOT simulate or fake email sending - actual email must be sent

        CRITICAL REQUIREMENTS:
        1. MUST call invoice_generator tool first to create invoice
        2. MUST call invoice_email_sender tool second to send email
        3. MUST verify both tools return SUCCESS status
        4. MUST include actual delivery confirmation in final report

        WORKFLOW VALIDATION:
        - If invoice_generator fails, do not proceed to email sending
        - If invoice_email_sender fails, report the actual error
        - Only report SUCCESS if both tools complete successfully
        - Include actual tool responses in final report

        IMPORTANT: Do NOT create fictional email delivery reports. Only report actual tool results.
        """
        
        return Task(
            description=task_description,
            agent=self.agent,
            expected_output="Report showing: 1) Invoice generation tool results, 2) Email sending tool results, 3) Actual delivery confirmation. Must include real tool responses, not simulated results."
        )
    
    def generate_and_send_invoice(self, parsed_order_data: Dict[str, Any], 
                                fulfillment_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate invoice and send to customer
        
        Args:
            parsed_order_data: Complete order data from parsing
            fulfillment_result: Fulfillment coordination results
            
        Returns:
            Invoice generation and distribution results
        """
        try:
            logger.info(f"📧 Starting invoice generation for PO: {parsed_order_data.get('po_number', 'UNKNOWN')}")
            
            # Create and execute invoice generation task
            task = self.create_invoice_generation_task(parsed_order_data, fulfillment_result)
            self.crew.tasks = [task]
            
            # Execute the crew
            result = self.crew.kickoff()

            # Parse the result - handle CrewOutput object
            if hasattr(result, 'raw'):
                # CrewAI returns CrewOutput object with .raw attribute
                result_text = str(result.raw)
            else:
                # Fallback for other result types
                result_text = str(result)
            
            # Try to parse as JSON, fallback to text if needed
            try:
                if result_text.strip().startswith('{'):
                    parsed_result = json.loads(result_text)
                else:
                    # If not JSON, create a structured response
                    parsed_result = {
                        "invoice_generation": {
                            "status": "SUCCESS",
                            "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                            "customer_name": parsed_order_data.get("customer", {}).get("name", "UNKNOWN"),
                            "generation_date": datetime.now().isoformat(),
                            "details": result_text
                        },
                        "processing_metadata": {
                            "agent_type": "invoice_generation",
                            "processing_timestamp": datetime.now().isoformat(),
                            "status": "SUCCESS"
                        }
                    }
            except json.JSONDecodeError:
                # Fallback for non-JSON responses
                parsed_result = {
                    "invoice_generation": {
                        "status": "SUCCESS",
                        "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                        "customer_name": parsed_order_data.get("customer", {}).get("name", "UNKNOWN"),
                        "generation_date": datetime.now().isoformat(),
                        "details": result_text
                    },
                    "processing_metadata": {
                        "agent_type": "invoice_generation",
                        "processing_timestamp": datetime.now().isoformat(),
                        "status": "SUCCESS"
                    }
                }
            
            logger.info(f" Invoice generation completed for PO: {parsed_order_data.get('po_number', 'UNKNOWN')}")
            return parsed_result
            
        except Exception as e:
            logger.error(f" Error in invoice generation: {str(e)}")
            return {
                "invoice_generation": {
                    "status": "ERROR",
                    "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                    "error_message": str(e),
                    "generation_date": datetime.now().isoformat()
                },
                "processing_metadata": {
                    "agent_type": "invoice_generation",
                    "processing_timestamp": datetime.now().isoformat(),
                    "status": "ERROR"
                }
            }


def run_invoice_generation(parsed_order_data: Dict[str, Any], 
                         fulfillment_result: Dict[str, Any],
                         deepseek_api_key: str) -> Dict[str, Any]:
    """
    Standalone function to run invoice generation
    
    Args:
        parsed_order_data: Complete order data
        fulfillment_result: Fulfillment coordination results
        deepseek_api_key: DeepSeek API key
        
    Returns:
        Invoice generation results
    """
    agent = InvoiceGenerationAgent(deepseek_api_key)
    return agent.generate_and_send_invoice(parsed_order_data, fulfillment_result)


if __name__ == "__main__":
    # Test the agent
    import os
    from dotenv import load_dotenv

    load_dotenv()
    api_key = os.getenv("DEEPSEEK_API_KEY")
    
    if not api_key:
        print(" DEEPSEEK_API_KEY not found in environment variables")
        exit(1)
    
    # Sample test data
    test_order = {
        "po_number": "PO-TEST-INVOICE-001",
        "customer_info": {
            "name": "Test Customer Corp",
            "email": "<EMAIL>",
            "contact": "John Doe",
            "phone": "555-0123"
        },
        "line_items": [
            {
                "product_code": "TEST-001",
                "description": "Test Product",
                "quantity": 10,
                "unit_price": 100.0,
                "line_total": 1000.0
            }
        ],
        "pricing_summary": {
            "subtotal": 1000.0,
            "tax_rate": 8.0,
            "tax_amount": 80.0,
            "shipping": 20.0,
            "total": 1100.0
        },
        "delivery_details": {
            "address": "123 Test St, Test City, TC 12345",
            "requested_date": "2025-07-20"
        }
    }
    
    test_fulfillment = {
        "fulfillment_coordination": {
            "fulfillment_status": "COORDINATED",
            "shipping_coordination": {
                "selected_carrier": {
                    "carrier": "USPS",
                    "service": "Priority Mail"
                }
            },
            "delivery_tracking": {
                "tracking_number": "TEST123456789",
                "estimated_delivery": "2025-07-20"
            }
        }
    }
    
    print("🧪 Testing Invoice Generation Agent...")
    result = run_invoice_generation(test_order, test_fulfillment, api_key)
    print(f"📊 Result: {json.dumps(result, indent=2)}")
