#!/usr/bin/env python3
"""
Credit Management Demo
Demonstrates the credit management functionality without external dependencies
"""

import json
from datetime import datetime

def demo_customer_credit_analysis():
    """Demo customer credit analysis"""
    print("1. 👤 Customer Credit Analysis")
    print("-" * 40)
    
    # Mock credit database
    customer_name = "ABC Manufacturing Corp"
    order_amount = 8811.79
    
    credit_data = {
        "credit_score": 785,
        "credit_rating": "A-",
        "established_date": "2018-03-15",
        "industry": "Manufacturing",
        "annual_revenue": 25000000,
        "employees": 150,
        "credit_limit": 50000,
        "current_balance": 12500,
        "available_credit": 37500,
        "financial_strength": "Strong"
    }
    
    # Calculate utilization
    utilization = (credit_data["current_balance"] / credit_data["credit_limit"]) * 100
    
    # Risk assessment
    risk_score = 0
    if credit_data["credit_score"] >= 750:
        risk_score += 10
    if utilization <= 60:
        risk_score += 0
    else:
        risk_score += 15
    
    if order_amount <= credit_data["available_credit"]:
        risk_score += 0
    else:
        risk_score += 30
    
    if risk_score <= 25:
        risk_level = "LOW"
        recommendation = "APPROVE"
    else:
        risk_level = "MEDIUM"
        recommendation = "APPROVE_WITH_CONDITIONS"
    
    print(f"Customer: {customer_name}")
    print(f"Credit Score: {credit_data['credit_score']}")
    print(f"Credit Rating: {credit_data['credit_rating']}")
    print(f"Credit Limit: ${credit_data['credit_limit']:,.2f}")
    print(f"Available Credit: ${credit_data['available_credit']:,.2f}")
    print(f"Current Utilization: {utilization:.1f}%")
    print(f"Order Amount: ${order_amount:,.2f}")
    print(f"Risk Score: {risk_score}")
    print(f"Risk Level: {risk_level}")
    print(f"Recommendation: {recommendation}")
    
    return {
        "customer": customer_name,
        "credit_score": credit_data["credit_score"],
        "risk_level": risk_level,
        "recommendation": recommendation
    }

def demo_payment_history_analysis():
    """Demo payment history analysis"""
    print("\n2. 💳 Payment History Analysis")
    print("-" * 40)
    
    customer_name = "ABC Manufacturing Corp"
    
    payment_data = {
        "total_invoices": 24,
        "paid_on_time": 20,
        "paid_late": 4,
        "average_days_to_pay": 28,
        "longest_delay": 45,
        "disputes": 1,
        "payment_trend": "IMPROVING"
    }
    
    on_time_percentage = (payment_data["paid_on_time"] / payment_data["total_invoices"]) * 100
    
    if on_time_percentage >= 90 and payment_data["average_days_to_pay"] <= 35:
        payment_risk = "LOW"
    elif on_time_percentage >= 75:
        payment_risk = "MEDIUM"
    else:
        payment_risk = "HIGH"
    
    print(f"Customer: {customer_name}")
    print(f"Total Invoices: {payment_data['total_invoices']}")
    print(f"On-time Payments: {payment_data['paid_on_time']} ({on_time_percentage:.1f}%)")
    print(f"Late Payments: {payment_data['paid_late']}")
    print(f"Average Days to Pay: {payment_data['average_days_to_pay']}")
    print(f"Payment Trend: {payment_data['payment_trend']}")
    print(f"Payment Risk: {payment_risk}")
    
    return {
        "payment_risk": payment_risk,
        "on_time_percentage": on_time_percentage,
        "average_days": payment_data["average_days_to_pay"]
    }

def demo_financial_risk_calculation():
    """Demo financial risk calculation"""
    print("\n3. 🎯 Financial Risk Assessment")
    print("-" * 40)
    
    customer_name = "ABC Manufacturing Corp"
    order_amount = 8811.79
    
    financial_data = {
        "debt_to_equity": 0.45,
        "current_ratio": 2.1,
        "quick_ratio": 1.8,
        "profit_margin": 8.5,
        "revenue_growth": 12.0,
        "annual_revenue": 25000000
    }
    
    # Calculate risk score
    risk_score = 0
    risk_factors = []
    
    if financial_data["current_ratio"] >= 1.5:
        risk_score += 0
    else:
        risk_score += 15
        risk_factors.append("Liquidity concerns")
    
    if financial_data["debt_to_equity"] <= 0.6:
        risk_score += 0
    else:
        risk_score += 10
        risk_factors.append("High leverage")
    
    if financial_data["profit_margin"] >= 5:
        risk_score += 0
    else:
        risk_score += 20
        risk_factors.append("Low profitability")
    
    order_percentage = (order_amount / financial_data["annual_revenue"]) * 100
    if order_percentage <= 1:
        risk_score += 0
    else:
        risk_score += 10
        risk_factors.append("Large order relative to revenue")
    
    if risk_score <= 20:
        financial_risk = "LOW"
    elif risk_score <= 40:
        financial_risk = "MEDIUM"
    else:
        financial_risk = "HIGH"
    
    print(f"Customer: {customer_name}")
    print(f"Current Ratio: {financial_data['current_ratio']}")
    print(f"Debt-to-Equity: {financial_data['debt_to_equity']}")
    print(f"Profit Margin: {financial_data['profit_margin']}%")
    print(f"Revenue Growth: {financial_data['revenue_growth']}%")
    print(f"Order % of Revenue: {order_percentage:.2f}%")
    print(f"Financial Risk Score: {risk_score}")
    print(f"Financial Risk Level: {financial_risk}")
    if risk_factors:
        print(f"Risk Factors: {', '.join(risk_factors)}")
    
    return {
        "financial_risk": financial_risk,
        "risk_score": risk_score,
        "risk_factors": risk_factors
    }

def demo_credit_decision_engine():
    """Demo credit decision engine"""
    print("\n4. ⚖️ Credit Decision Engine")
    print("-" * 40)
    
    customer_name = "ABC Manufacturing Corp"
    order_amount = 8811.79
    credit_score = 785
    payment_risk = "LOW"
    financial_risk = "LOW"
    
    # Decision matrix
    decision_score = 0
    conditions = []
    
    # Credit score factor
    if credit_score >= 750:
        decision_score += 0
    elif credit_score >= 700:
        decision_score += 10
    else:
        decision_score += 25
        conditions.append("Credit score below preferred threshold")
    
    # Payment risk factor
    if payment_risk == "LOW":
        decision_score += 0
    elif payment_risk == "MEDIUM":
        decision_score += 15
    else:
        decision_score += 30
        conditions.append("Poor payment history")
    
    # Financial risk factor
    if financial_risk == "LOW":
        decision_score += 0
    elif financial_risk == "MEDIUM":
        decision_score += 15
    else:
        decision_score += 30
        conditions.append("High financial risk")
    
    # Order size factor
    if order_amount <= 25000:
        decision_score += 0
    elif order_amount <= 50000:
        decision_score += 10
    else:
        decision_score += 20
        conditions.append("Large order amount")
    
    # Final decision
    if decision_score <= 15:
        decision = "APPROVE"
        payment_terms = "Net 30"
    elif decision_score <= 35:
        decision = "APPROVE_WITH_CONDITIONS"
        payment_terms = "Net 15"
    elif decision_score <= 55:
        decision = "MANUAL_REVIEW"
        payment_terms = "To be determined"
    else:
        decision = "DENY"
        payment_terms = "Prepayment required"
    
    print(f"Customer: {customer_name}")
    print(f"Order Amount: ${order_amount:,.2f}")
    print(f"Credit Score: {credit_score}")
    print(f"Payment Risk: {payment_risk}")
    print(f"Financial Risk: {financial_risk}")
    print(f"Decision Score: {decision_score}")
    print(f"FINAL DECISION: {decision}")
    print(f"Payment Terms: {payment_terms}")
    if conditions:
        print(f"Conditions: {', '.join(conditions)}")
    
    return {
        "decision": decision,
        "payment_terms": payment_terms,
        "decision_score": decision_score,
        "conditions": conditions
    }

def main():
    """Run the complete credit management demo"""
    print(" CREDIT MANAGEMENT & RISK ASSESSMENT DEMO")
    print("=" * 60)
    print("Demonstrating enterprise-grade credit decision making")
    print("=" * 60)
    
    # Run all demos
    credit_analysis = demo_customer_credit_analysis()
    payment_analysis = demo_payment_history_analysis()
    financial_analysis = demo_financial_risk_calculation()
    final_decision = demo_credit_decision_engine()
    
    # Summary
    print("\n🎯 EXECUTIVE SUMMARY")
    print("=" * 40)
    print(f"Customer: {credit_analysis['customer']}")
    print(f"Credit Score: {credit_analysis['credit_score']}")
    print(f"Payment Performance: {payment_analysis['on_time_percentage']:.1f}% on-time")
    print(f"Financial Stability: Strong")
    print(f"Overall Risk: {final_decision['decision_score']} points")
    print(f"FINAL DECISION: {final_decision['decision']}")
    print(f"Recommended Terms: {final_decision['payment_terms']}")
    
    if final_decision['decision'] == 'APPROVE':
        print("\n ORDER APPROVED - Process immediately")
    elif final_decision['decision'] == 'APPROVE_WITH_CONDITIONS':
        print("\n⚠️ ORDER APPROVED WITH CONDITIONS - Apply special terms")
    elif final_decision['decision'] == 'MANUAL_REVIEW':
        print("\n👥 MANUAL REVIEW REQUIRED - Escalate to credit manager")
    else:
        print("\n ORDER DENIED - Offer alternative payment terms")
    
    print("\n🎉 Credit Management Demo Complete!")

if __name__ == "__main__":
    main()
