#!/usr/bin/env python3
"""
Fulfillment Coordination Tools
Advanced fulfillment orchestration and logistics management tools
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Import tool decorator
from crewai.tools import tool

# Import database connection
try:
    from database.db_connection import get_master_data_db
    from config.database_config import initialize_database_if_needed

    # Initialize database if needed
    initialize_database_if_needed()
    
    DATABASE_AVAILABLE = True
    
except ImportError:
    print("Warning: Database modules not available, using mock data")
    DATABASE_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@tool
def warehouse_optimizer(order_data: str) -> str:
    """
    Optimize warehouse allocation and fulfillment planning
    
    Args:
        order_data: JSON string containing order details with line items and delivery requirements
        
    Returns:
        JSON string with optimized warehouse allocation and fulfillment plan
    """
    try:
        logger.info("🏭 Optimizing warehouse allocation for fulfillment")
        
        order = json.loads(order_data)
        po_number = order.get("po_number", "UNKNOWN")
        line_items = order.get("line_items", [])
        delivery_details = order.get("delivery_details", {})
        
        # Mock warehouse optimization logic
        warehouse_plan = {
            "po_number": po_number,
            "optimization_date": datetime.now().isoformat(),
            "selected_warehouse": "MAIN_WAREHOUSE_CA",
            "warehouse_details": {
                "warehouse_id": "WH-001",
                "location": "Anytown, CA",
                "distance_to_delivery": "15 miles",
                "capacity_utilization": 75.2,
                "processing_time_hours": 4
            },
            "fulfillment_plan": [],
            "optimization_metrics": {
                "total_pick_time_minutes": 0,
                "shipping_cost_estimate": 0.0,
                "delivery_time_estimate_hours": 24,
                "efficiency_score": 95.5
            }
        }
        
        total_pick_time = 0
        total_shipping_cost = 0
        
        for item in line_items:
            product_code = item.get("product_code", "")
            quantity = item.get("quantity", 0)
            
            # Calculate pick time and shipping cost
            pick_time = quantity * 0.5  # 0.5 minutes per unit
            shipping_cost = quantity * 2.0  # $2 per unit shipping
            
            total_pick_time += pick_time
            total_shipping_cost += shipping_cost
            
            fulfillment_item = {
                "product_code": product_code,
                "quantity": quantity,
                "warehouse_location": f"Aisle-{product_code[:3]}-Bin-{product_code[-3:]}",
                "pick_sequence": len(warehouse_plan["fulfillment_plan"]) + 1,
                "estimated_pick_time_minutes": pick_time,
                "shipping_cost": shipping_cost,
                "special_handling": "STANDARD" if quantity < 100 else "BULK_HANDLING"
            }
            
            warehouse_plan["fulfillment_plan"].append(fulfillment_item)
        
        # Update totals
        warehouse_plan["optimization_metrics"]["total_pick_time_minutes"] = total_pick_time
        warehouse_plan["optimization_metrics"]["shipping_cost_estimate"] = total_shipping_cost
        
        logger.info(f" Warehouse optimization completed for {po_number}")
        return json.dumps(warehouse_plan, indent=2)
        
    except Exception as e:
        logger.error(f" Warehouse optimization failed: {str(e)}")
        return json.dumps({
            "error": f"Warehouse optimization failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


@tool
def pick_list_generator(warehouse_plan: str) -> str:
    """
    Generate optimized pick lists and work orders for warehouse operations
    
    Args:
        warehouse_plan: JSON string containing warehouse allocation plan
        
    Returns:
        JSON string with generated pick lists and work orders
    """
    try:
        logger.info(" Generating pick lists and work orders")
        
        plan = json.loads(warehouse_plan)
        po_number = plan.get("po_number", "UNKNOWN")
        fulfillment_plan = plan.get("fulfillment_plan", [])
        
        pick_list = {
            "po_number": po_number,
            "pick_list_id": f"PL-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "generated_at": datetime.now().isoformat(),
            "warehouse_id": plan.get("warehouse_details", {}).get("warehouse_id", "WH-001"),
            "priority": "STANDARD",
            "estimated_completion_time": (datetime.now() + timedelta(hours=4)).isoformat(),
            "pick_items": [],
            "work_orders": [],
            "special_instructions": []
        }
        
        # Generate pick items
        for item in fulfillment_plan:
            pick_item = {
                "sequence": item.get("pick_sequence", 1),
                "product_code": item.get("product_code", ""),
                "quantity": item.get("quantity", 0),
                "location": item.get("warehouse_location", ""),
                "handling_type": item.get("special_handling", "STANDARD"),
                "estimated_time_minutes": item.get("estimated_pick_time_minutes", 0),
                "picker_notes": f"Handle with care - {item.get('special_handling', 'STANDARD')} processing"
            }
            pick_list["pick_items"].append(pick_item)
        
        # Generate work orders
        work_orders = [
            {
                "work_order_id": f"WO-PICK-{po_number}",
                "type": "PICKING",
                "priority": "STANDARD",
                "assigned_to": "AUTO_ASSIGN",
                "estimated_duration_minutes": plan.get("optimization_metrics", {}).get("total_pick_time_minutes", 60),
                "status": "PENDING"
            },
            {
                "work_order_id": f"WO-PACK-{po_number}",
                "type": "PACKING",
                "priority": "STANDARD",
                "assigned_to": "AUTO_ASSIGN",
                "estimated_duration_minutes": 30,
                "status": "PENDING",
                "dependencies": [f"WO-PICK-{po_number}"]
            },
            {
                "work_order_id": f"WO-SHIP-{po_number}",
                "type": "SHIPPING",
                "priority": "STANDARD",
                "assigned_to": "AUTO_ASSIGN",
                "estimated_duration_minutes": 15,
                "status": "PENDING",
                "dependencies": [f"WO-PACK-{po_number}"]
            }
        ]
        
        pick_list["work_orders"] = work_orders
        
        # Add special instructions
        total_items = sum(item.get("quantity", 0) for item in fulfillment_plan)
        if total_items > 500:
            pick_list["special_instructions"].append("BULK_ORDER: Use forklift for large quantities")
        
        if any(item.get("special_handling") == "BULK_HANDLING" for item in fulfillment_plan):
            pick_list["special_instructions"].append("SPECIAL_HANDLING: Some items require bulk handling procedures")
        
        logger.info(f" Pick list generated for {po_number}: {pick_list['pick_list_id']}")
        return json.dumps(pick_list, indent=2)
        
    except Exception as e:
        logger.error(f" Pick list generation failed: {str(e)}")
        return json.dumps({
            "error": f"Pick list generation failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


@tool
def shipping_carrier_selector(order_data: str, delivery_requirements: str) -> str:
    """
    Select optimal shipping carrier and service level based on requirements and cost
    
    Args:
        order_data: JSON string containing order details
        delivery_requirements: JSON string with delivery preferences and constraints
        
    Returns:
        JSON string with selected carrier and shipping details
    """
    try:
        logger.info("🚚 Selecting optimal shipping carrier")
        
        order = json.loads(order_data)
        requirements = json.loads(delivery_requirements)
        
        po_number = order.get("po_number", "UNKNOWN")
        delivery_details = order.get("delivery_details", {})
        total_weight = sum(item.get("quantity", 0) * 2.5 for item in order.get("line_items", []))  # Assume 2.5 lbs per unit
        
        # Mock carrier options
        carrier_options = [
            {
                "carrier": "FedEx",
                "service": "Ground",
                "cost": 45.50,
                "transit_days": 3,
                "delivery_date": (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d"),
                "tracking_available": True,
                "insurance_included": True,
                "weight_limit_lbs": 150
            },
            {
                "carrier": "UPS",
                "service": "Ground",
                "cost": 42.75,
                "transit_days": 3,
                "delivery_date": (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d"),
                "tracking_available": True,
                "insurance_included": True,
                "weight_limit_lbs": 150
            },
            {
                "carrier": "USPS",
                "service": "Priority Mail",
                "cost": 38.25,
                "transit_days": 2,
                "delivery_date": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
                "tracking_available": True,
                "insurance_included": False,
                "weight_limit_lbs": 70
            }
        ]
        
        # Filter carriers by weight capacity
        suitable_carriers = [c for c in carrier_options if c["weight_limit_lbs"] >= total_weight]
        
        if not suitable_carriers:
            suitable_carriers = carrier_options  # Fallback to all carriers
        
        # Select best carrier (lowest cost with acceptable transit time)
        selected_carrier = min(suitable_carriers, key=lambda x: (x["cost"], x["transit_days"]))
        
        shipping_selection = {
            "po_number": po_number,
            "selection_date": datetime.now().isoformat(),
            "selected_carrier": selected_carrier,
            "alternative_options": [c for c in suitable_carriers if c != selected_carrier],
            "shipment_details": {
                "estimated_weight_lbs": total_weight,
                "package_count": 1 if total_weight <= 50 else 2,
                "dimensions": "24x18x12 inches" if total_weight <= 50 else "Multiple packages",
                "declared_value": order.get("pricing", {}).get("total", 0),
                "special_services": []
            },
            "delivery_confirmation": {
                "signature_required": total_weight > 100 or order.get("pricing", {}).get("total", 0) > 1000,
                "delivery_instructions": delivery_details.get("special_instructions", ""),
                "delivery_address": delivery_details.get("address", "")
            }
        }
        
        # Add special services based on value
        if order.get("pricing", {}).get("total", 0) > 1000:
            shipping_selection["shipment_details"]["special_services"].append("INSURANCE")
        
        if requirements.get("expedited", False):
            shipping_selection["shipment_details"]["special_services"].append("EXPEDITED")
        
        logger.info(f" Carrier selected for {po_number}: {selected_carrier['carrier']} {selected_carrier['service']}")
        return json.dumps(shipping_selection, indent=2)
        
    except Exception as e:
        logger.error(f" Carrier selection failed: {str(e)}")
        return json.dumps({
            "error": f"Carrier selection failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


@tool
def shipping_label_creator(shipping_selection: str, pick_list: str) -> str:
    """
    Create shipping labels and documentation for selected carrier
    
    Args:
        shipping_selection: JSON string with selected carrier and shipping details
        pick_list: JSON string with pick list and packaging information
        
    Returns:
        JSON string with shipping label details and tracking information
    """
    try:
        logger.info("🏷️ Creating shipping labels and documentation")
        
        selection = json.loads(shipping_selection)
        pick_data = json.loads(pick_list)
        
        po_number = selection.get("po_number", "UNKNOWN")
        carrier = selection.get("selected_carrier", {})
        
        # Generate tracking number
        tracking_number = f"{carrier.get('carrier', 'UNK')[:3].upper()}{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        shipping_labels = {
            "po_number": po_number,
            "label_created_at": datetime.now().isoformat(),
            "tracking_number": tracking_number,
            "carrier_details": carrier,
            "shipping_labels": [
                {
                    "label_id": f"LBL-{tracking_number}-001",
                    "package_number": 1,
                    "tracking_number": tracking_number,
                    "carrier": carrier.get("carrier", ""),
                    "service": carrier.get("service", ""),
                    "label_format": "PDF",
                    "label_size": "4x6",
                    "postage_cost": carrier.get("cost", 0)
                }
            ],
            "shipping_documentation": {
                "commercial_invoice": f"INV-{po_number}",
                "packing_slip": f"PS-{po_number}",
                "bill_of_lading": f"BOL-{po_number}",
                "customs_forms": None  # Domestic shipment
            },
            "pickup_schedule": {
                "pickup_date": datetime.now().strftime("%Y-%m-%d"),
                "pickup_time_window": "2:00 PM - 5:00 PM",
                "pickup_location": "Shipping Dock A",
                "special_instructions": "Call 30 minutes before pickup"
            },
            "delivery_tracking": {
                "estimated_delivery": carrier.get("delivery_date", ""),
                "tracking_url": f"https://{carrier.get('carrier', 'carrier').lower()}.com/tracking/{tracking_number}",
                "delivery_notifications": {
                    "email_notifications": True,
                    "sms_notifications": False,
                    "delivery_confirmation": True
                }
            }
        }
        
        # Add multiple packages if needed
        package_count = selection.get("shipment_details", {}).get("package_count", 1)
        if package_count > 1:
            for i in range(2, package_count + 1):
                additional_tracking = f"{carrier.get('carrier', 'UNK')[:3].upper()}{datetime.now().strftime('%Y%m%d%H%M%S')}{i:02d}"
                shipping_labels["shipping_labels"].append({
                    "label_id": f"LBL-{additional_tracking}-{i:03d}",
                    "package_number": i,
                    "tracking_number": additional_tracking,
                    "carrier": carrier.get("carrier", ""),
                    "service": carrier.get("service", ""),
                    "label_format": "PDF",
                    "label_size": "4x6",
                    "postage_cost": 0  # Additional packages often have reduced cost
                })
        
        logger.info(f" Shipping labels created for {po_number}: {tracking_number}")
        return json.dumps(shipping_labels, indent=2)
        
    except Exception as e:
        logger.error(f" Shipping label creation failed: {str(e)}")
        return json.dumps({
            "error": f"Shipping label creation failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


@tool
def delivery_tracker(tracking_info: str) -> str:
    """
    Track delivery status and provide real-time updates
    
    Args:
        tracking_info: JSON string with tracking numbers and carrier information
        
    Returns:
        JSON string with current delivery status and tracking updates
    """
    try:
        logger.info("📍 Tracking delivery status")
        
        tracking = json.loads(tracking_info)
        po_number = tracking.get("po_number", "UNKNOWN")
        tracking_number = tracking.get("tracking_number", "")
        
        # Mock tracking status
        tracking_events = [
            {
                "timestamp": datetime.now().isoformat(),
                "status": "LABEL_CREATED",
                "description": "Shipping label created",
                "location": "Origin Facility"
            },
            {
                "timestamp": (datetime.now() + timedelta(hours=2)).isoformat(),
                "status": "PICKED_UP",
                "description": "Package picked up by carrier",
                "location": "Origin Facility"
            },
            {
                "timestamp": (datetime.now() + timedelta(hours=6)).isoformat(),
                "status": "IN_TRANSIT",
                "description": "Package in transit to destination",
                "location": "Regional Sort Facility"
            }
        ]
        
        delivery_status = {
            "po_number": po_number,
            "tracking_number": tracking_number,
            "current_status": "IN_TRANSIT",
            "last_updated": datetime.now().isoformat(),
            "estimated_delivery": tracking.get("delivery_tracking", {}).get("estimated_delivery", ""),
            "tracking_events": tracking_events,
            "delivery_progress": {
                "percentage_complete": 45,
                "current_location": "Regional Sort Facility",
                "next_milestone": "Out for Delivery",
                "estimated_next_update": (datetime.now() + timedelta(hours=12)).isoformat()
            },
            "delivery_exceptions": [],
            "customer_notifications": {
                "notifications_sent": 2,
                "last_notification": datetime.now().isoformat(),
                "next_notification": "On delivery day"
            }
        }
        
        logger.info(f" Delivery tracking updated for {po_number}: {tracking_number}")
        return json.dumps(delivery_status, indent=2)
        
    except Exception as e:
        logger.error(f" Delivery tracking failed: {str(e)}")
        return json.dumps({
            "error": f"Delivery tracking failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


@tool
def fulfillment_exception_handler(exception_data: str) -> str:
    """
    Handle fulfillment exceptions and provide resolution recommendations
    
    Args:
        exception_data: JSON string containing exception details and context
        
    Returns:
        JSON string with exception analysis and resolution plan
    """
    try:
        logger.info("⚠️ Handling fulfillment exception")
        
        exception = json.loads(exception_data)
        po_number = exception.get("po_number", "UNKNOWN")
        exception_type = exception.get("exception_type", "UNKNOWN")
        
        # Mock exception handling
        resolution_plan = {
            "po_number": po_number,
            "exception_id": f"EXC-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "exception_type": exception_type,
            "severity": "MEDIUM",
            "detected_at": datetime.now().isoformat(),
            "resolution_plan": {
                "immediate_actions": [],
                "escalation_required": False,
                "estimated_resolution_time": "2-4 hours",
                "alternative_solutions": []
            },
            "impact_assessment": {
                "delivery_delay_days": 0,
                "cost_impact": 0.0,
                "customer_impact": "LOW"
            },
            "notifications": {
                "customer_notification_required": False,
                "internal_escalation": False,
                "management_alert": False
            }
        }
        
        # Handle different exception types
        if exception_type == "INVENTORY_SHORTAGE":
            resolution_plan["resolution_plan"]["immediate_actions"] = [
                "Check alternative warehouse locations",
                "Contact supplier for expedited delivery",
                "Offer partial shipment to customer"
            ]
            resolution_plan["impact_assessment"]["delivery_delay_days"] = 2
            resolution_plan["notifications"]["customer_notification_required"] = True
            
        elif exception_type == "SHIPPING_DELAY":
            resolution_plan["resolution_plan"]["immediate_actions"] = [
                "Contact carrier for updated delivery estimate",
                "Explore alternative shipping options",
                "Notify customer of delay"
            ]
            resolution_plan["impact_assessment"]["delivery_delay_days"] = 1
            resolution_plan["notifications"]["customer_notification_required"] = True
            
        elif exception_type == "DAMAGED_GOODS":
            resolution_plan["resolution_plan"]["immediate_actions"] = [
                "Quarantine damaged items",
                "Prepare replacement shipment",
                "Document damage for insurance claim"
            ]
            resolution_plan["severity"] = "HIGH"
            resolution_plan["notifications"]["management_alert"] = True
            
        else:
            resolution_plan["resolution_plan"]["immediate_actions"] = [
                "Investigate exception details",
                "Contact fulfillment team lead",
                "Document exception for analysis"
            ]
        
        logger.info(f" Exception handling plan created for {po_number}: {exception_type}")
        return json.dumps(resolution_plan, indent=2)
        
    except Exception as e:
        logger.error(f" Exception handling failed: {str(e)}")
        return json.dumps({
            "error": f"Exception handling failed: {str(e)}",
            "po_number": "UNKNOWN",
            "status": "FAILED"
        })


def get_fulfillment_coordination_tools():
    """Get all fulfillment coordination tools"""
    return [
        warehouse_optimizer,
        pick_list_generator,
        shipping_carrier_selector,
        shipping_label_creator,
        delivery_tracker,
        fulfillment_exception_handler
    ]


if __name__ == "__main__":
    # Test the tools
    print("🧪 Testing Fulfillment Coordination Tools...")

    # Test data
    test_order = {
        "po_number": "PO-TEST-001",
        "line_items": [
            {"product_code": "WIDGET-A100", "quantity": 50},
            {"product_code": "BOLT-M8-50", "quantity": 200}
        ],
        "delivery_details": {
            "address": "123 Test St, Test City, CA 12345",
            "requested_date": "2025-07-20"
        },
        "pricing": {"total": 5000.0}
    }

    # Test warehouse optimization
    print("\n1. Testing warehouse optimization...")
    warehouse_result = warehouse_optimizer._run(json.dumps(test_order))
    print(" Warehouse optimization completed")

    # Test pick list generation
    print("\n2. Testing pick list generation...")
    pick_result = pick_list_generator._run(warehouse_result)
    print(" Pick list generation completed")

    print("\n🎉 All fulfillment coordination tools tested successfully!")
