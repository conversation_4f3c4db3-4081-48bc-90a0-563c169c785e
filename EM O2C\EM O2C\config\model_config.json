{"model_aliases": {"primary_llm": {"display_name": "Enterprise-LLM-v1", "actual_model": "deepseek/deepseek-chat", "provider": "deepseek", "description": "Primary language model for O2C operations"}, "parsing_llm": {"display_name": "Document-Parser-v2", "actual_model": "deepseek/deepseek-chat", "provider": "deepseek", "description": "Specialized model for document parsing"}, "validation_llm": {"display_name": "Business-Validator-v1", "actual_model": "deepseek/deepseek-chat", "provider": "deepseek", "description": "Model for business rule validation"}}, "logging_config": {"hide_model_names": true, "use_aliases_in_logs": true, "suppress_cost_logging": true, "suppress_provider_info": true}, "security_settings": {"mask_api_keys": true, "hide_provider_details": true, "use_generic_model_names": true}}