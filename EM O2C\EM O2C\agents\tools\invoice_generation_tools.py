#!/usr/bin/env python3
"""
Invoice Generation Tools for O2C System
Tools for creating and distributing invoices to customers
"""

import os
import json
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, Optional
from crewai.tools import tool
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@tool
def invoice_generator(order_data: str, fulfillment_data: str) -> str:
    """
    Generate professional TXT format invoices based on order and fulfillment data

    Args:
        order_data: JSON string containing complete order information
        fulfillment_data: JSON string containing fulfillment and shipping details

    Returns:
        JSON string with generated invoice details and file path
    """
    """Generate TXT invoice from order and fulfillment data"""
    try:
        logger.info("📄 Generating TXT invoice...")

        # Parse input data
        order = json.loads(order_data)
        fulfillment = json.loads(fulfillment_data)

        # Extract key information
        po_number = order.get("po_number", "UNKNOWN")
        customer_info = order.get("customer", {})
        customer_name = customer_info.get("name", "UNKNOWN")
        line_items = order.get("line_items", [])
        pricing_summary = order.get("pricing", {})
        delivery_details = order.get("delivery", {})

        # Generate invoice number and date
        invoice_number = f"INV-{po_number.replace('PO-', '')}"
        invoice_date = datetime.now().strftime("%Y-%m-%d")
        due_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

        # Get fulfillment details
        fulfillment_coord = fulfillment.get("fulfillment_coordination", {})
        shipping_details = fulfillment_coord.get("shipping_coordination", {})
        tracking_info = fulfillment_coord.get("delivery_tracking", {})

        # Create invoice content
        invoice_content = _create_invoice_content(
            invoice_number, invoice_date, due_date, po_number,
            customer_info, line_items, pricing_summary,
            delivery_details, shipping_details, tracking_info
        )

        # Save invoice to file
        invoice_filename = f"INVOICE_{invoice_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        invoice_path = os.path.join("data/invoices", invoice_filename)

        # Ensure directory exists
        os.makedirs("data/invoices", exist_ok=True)

        # Write invoice file
        with open(invoice_path, 'w') as f:
            f.write(invoice_content)

        logger.info(f" Invoice generated: {invoice_filename}")

        return json.dumps({
            "status": "SUCCESS",
            "invoice_number": invoice_number,
            "invoice_date": invoice_date,
            "due_date": due_date,
            "invoice_filename": invoice_filename,
            "invoice_path": invoice_path,
            "customer_email": customer_info.get("contact", {}).get("email", ""),
            "customer_name": customer_name,
            "total_amount": pricing_summary.get("total", 0),
            "po_number": po_number,
            "invoice_content": invoice_content
        })

    except Exception as e:
        logger.error(f" Error generating invoice: {str(e)}")
        return json.dumps({
            "status": "ERROR",
            "message": f"Failed to generate invoice: {str(e)}"
        })

def _create_invoice_content(invoice_number: str, invoice_date: str,
                          due_date: str, po_number: str, customer_info: Dict,
                          line_items: list, pricing_summary: Dict,
                          delivery_details: Dict, shipping_details: Dict,
                          tracking_info: Dict) -> str:
        """Create formatted invoice content"""
        
        content = f"""
================================================================================
                                   INVOICE
================================================================================

INVOICE NUMBER: {invoice_number}
INVOICE DATE: {invoice_date}
DUE DATE: {due_date}
PURCHASE ORDER: {po_number}

================================================================================
BILL TO:
================================================================================
{customer_info.get('name', 'N/A')}
{customer_info.get('contact', {}).get('person', 'N/A')}
{customer_info.get('contact', {}).get('email', 'N/A')}
{customer_info.get('contact', {}).get('phone', 'N/A')}

================================================================================
SHIP TO:
================================================================================
{delivery_details.get('address', 'N/A')}
Requested Delivery: {delivery_details.get('requested_date', 'N/A')}

================================================================================
SHIPPING INFORMATION:
================================================================================
Carrier: {shipping_details.get('selected_carrier', {}).get('carrier', 'N/A')}
Service: {shipping_details.get('selected_carrier', {}).get('service', 'N/A')}
Tracking Number: {tracking_info.get('tracking_number', 'N/A')}
Estimated Delivery: {tracking_info.get('estimated_delivery', 'N/A')}

================================================================================
LINE ITEMS:
================================================================================
"""
        
        # Add line items
        subtotal = 0
        for i, item in enumerate(line_items, 1):
            line_total = item.get('line_total', 0)
            subtotal += line_total
            content += f"""
{i:2d}. {item.get('product_code', 'N/A')} - {item.get('description', 'N/A')}
    Quantity: {item.get('quantity', 0):,}
    Unit Price: ${item.get('unit_price', 0):,.2f}
    Line Total: ${line_total:,.2f}
"""
        
        # Add pricing summary
        tax_amount = pricing_summary.get('tax_amount', 0)
        shipping_cost = pricing_summary.get('shipping', 0)
        total = pricing_summary.get('total', 0)
        
        content += f"""
================================================================================
PRICING SUMMARY:
================================================================================
Subtotal:                                                    ${subtotal:,.2f}
Tax ({pricing_summary.get('tax_rate', 0)}%):                                                      ${tax_amount:,.2f}
Shipping:                                                    ${shipping_cost:,.2f}
                                                            ----------------
TOTAL AMOUNT DUE:                                           ${total:,.2f}

================================================================================
PAYMENT TERMS:
================================================================================
Payment Terms: Net 30 Days
Payment Due Date: {due_date}

Please remit payment within 30 days of invoice date.
For questions regarding this invoice, please contact our billing department.

================================================================================
THANK YOU FOR YOUR BUSINESS!
================================================================================

Invoice generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return content


@tool
def invoice_email_sender(invoice_data: str) -> str:
    """
    Send generated invoice to customer via email

    Args:
        invoice_data: JSON string containing invoice details and file path

    Returns:
        JSON string with email sending results
    """
    """Send invoice via email to customer"""
    try:
        logger.info("📧 Sending invoice email...")

        # Parse invoice data
        invoice = json.loads(invoice_data)

        # Email configuration (using the same credentials as email monitor)
        try:
            with open('config/email_config.json', 'r') as f:
                email_config = json.load(f)

            smtp_server = "smtp.gmail.com"
            smtp_port = 587
            sender_email = email_config.get("gmail", {}).get("username", "")
            sender_password = email_config.get("gmail", {}).get("app_password", "")

            if not sender_email or not sender_password:
                return json.dumps({
                    "status": "ERROR",
                    "message": "Email configuration not found or incomplete"
                })
        except Exception as e:
            return json.dumps({
                "status": "ERROR",
                "message": f"Failed to load email configuration: {str(e)}"
            })

        # Customer email
        customer_email = invoice.get("customer_email", "")
        customer_name = invoice.get("customer_name", "")
        invoice_number = invoice.get("invoice_number", "")
        total_amount = invoice.get("total_amount", 0)
        po_number = invoice.get("po_number", "")

        if not customer_email:
            return json.dumps({
                "status": "ERROR",
                "message": "Customer email not provided"
            })

        # Create email message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = customer_email
        msg['Subject'] = f"Invoice {invoice_number} - Purchase Order {po_number}"

        # Email body
        body = f"""Dear {customer_name},

Thank you for your recent purchase! Please find your invoice attached.

INVOICE DETAILS:
- Invoice Number: {invoice_number}
- Purchase Order: {po_number}
- Total Amount: ${total_amount:,.2f}
- Payment Terms: Net 30 Days

Your order has been processed and shipped. You should receive tracking information separately.

If you have any questions about this invoice, please don't hesitate to contact us.

Best regards,
Accounts Receivable Department
Your Company Name

---
This is an automated message from our Order-to-Cash system.
"""

        msg.attach(MIMEText(body, 'plain'))

        # Attach invoice file
        invoice_path = invoice.get("invoice_path", "")
        if invoice_path and os.path.exists(invoice_path):
            with open(invoice_path, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())

            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {os.path.basename(invoice_path)}'
            )
            msg.attach(part)

        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        text = msg.as_string()
        server.sendmail(sender_email, customer_email, text)
        server.quit()

        logger.info(f" Invoice email sent to {customer_email}")

        return json.dumps({
            "status": "SUCCESS",
            "message": f"Invoice {invoice_number} sent successfully to {customer_email}",
            "recipient": customer_email,
            "invoice_number": invoice_number,
            "sent_at": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f" Error sending invoice email: {str(e)}")
        return json.dumps({
            "status": "ERROR",
            "message": f"Failed to send invoice email: {str(e)}"
        })


def get_invoice_generation_tools():
    """Get all invoice generation tools"""
    return [
        invoice_generator,
        invoice_email_sender
    ]
