#!/usr/bin/env python3
"""
Master Data Setup Script
Quick setup script to initialize SQLite master data database for O2C system
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def main():
    """Main setup function"""
    print(" O2C Master Data Setup")
    print("=" * 40)
    
    try:
        # Import and run database setup
        from database.setup_database import O2CMasterDataSetup
        from config.database_config import get_database_path, show_database_info
        
        # Get database path
        db_path = get_database_path()
        
        print(f"📍 Database will be created at: {db_path}")
        
        # Initialize database
        setup = O2CMasterDataSetup(db_path)
        setup.setup_complete_database()
        
        print("\n" + "=" * 40)
        print("🎉 Master Data Setup Complete!")
        print("=" * 40)
        
        # Show database info
        show_database_info()
        
        print("\n Your O2C system now has SQLite master data!")
        print("🔧 You can now run agents with real database validation.")
        
        return True
        
    except Exception as e:
        print(f" Setup failed: {e}")
        print("\nPlease check the error above and try again.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
