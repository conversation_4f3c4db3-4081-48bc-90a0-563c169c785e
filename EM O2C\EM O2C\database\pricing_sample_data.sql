-- =====================================================
-- SAMPLE PRICING & CONTRACT DATA FOR O2C SYSTEM
-- =====================================================

-- Insert sample customer contracts
INSERT OR REPLACE INTO customer_contracts (
    contract_id, customer_id, contract_name, contract_type, status,
    effective_date, expiration_date, auto_renewal,
    base_discount_percent, volume_discount_tier, payment_terms, currency,
    minimum_order_value, maximum_order_value, annual_commitment,
    requires_approval_above, approval_workflow,
    created_date, created_by
) VALUES 
-- Acme Corporation - Enterprise Contract
('CONT-ACME-2024-001', 'CUST-001', 'Acme Corporation Enterprise Agreement', 'ENTERPRISE', 'ACTIVE',
 '2024-01-01', '2024-12-31', 1,
 15.0, 'TIER_2', 'Net 30', 'USD',
 1000.0, 100000.0, 500000.0,
 50000.0, 'DIRECTOR_APPROVAL',
 datetime('now'), 'system'),

-- XYZ Corp - Volume Contract
('CONT-XYZ-2024-002', 'CUST-002', 'XYZ Corp Volume Agreement', 'VOLUME', 'ACTIVE',
 '2024-03-01', '2025-02-28', 1,
 8.0, 'TIER_1', 'Net 15', 'USD',
 500.0, 25000.0, 150000.0,
 20000.0, 'MANAGER_APPROVAL',
 datetime('now'), 'system'),

-- Test Customer - Standard Contract
('CONT-TEST-2024-003', 'CUST-003', 'Test Customer Standard Terms', 'STANDARD', 'ACTIVE',
 '2024-01-01', '2025-12-31', 1,
 5.0, 'TIER_1', 'Net 30', 'USD',
 100.0, 10000.0, 50000.0,
 5000.0, 'MANAGER_APPROVAL',
 datetime('now'), 'system');

-- Insert contract pricing rules
INSERT OR REPLACE INTO contract_pricing_rules (
    contract_id, product_code, product_category,
    rule_type, rule_value, minimum_quantity, maximum_quantity,
    tier_name, tier_threshold,
    effective_date, expiration_date, is_active
) VALUES 
-- Acme Corporation Rules
('CONT-ACME-2024-001', 'WIDGET-A100', NULL, 'FIXED_PRICE', 110.0, 1, NULL, NULL, NULL, '2024-01-01', '2024-12-31', 1),
('CONT-ACME-2024-001', NULL, 'WIDGETS', 'VOLUME_TIER', 20.0, 100, NULL, 'TIER_2', 100, '2024-01-01', '2024-12-31', 1),
('CONT-ACME-2024-001', NULL, 'BOLTS', 'DISCOUNT_PERCENT', 10.0, 1, NULL, NULL, NULL, '2024-01-01', '2024-12-31', 1),

-- XYZ Corp Rules
('CONT-XYZ-2024-002', NULL, 'BOLTS', 'DISCOUNT_PERCENT', 12.0, 1, NULL, NULL, NULL, '2024-03-01', '2025-02-28', 1),
('CONT-XYZ-2024-002', NULL, 'WIDGETS', 'VOLUME_TIER', 15.0, 50, NULL, 'TIER_1', 50, '2024-03-01', '2025-02-28', 1),

-- Test Customer Rules
('CONT-TEST-2024-003', NULL, 'WIDGETS', 'DISCOUNT_PERCENT', 8.0, 1, NULL, NULL, NULL, '2024-01-01', '2025-12-31', 1);

-- Insert pricing matrices
INSERT OR REPLACE INTO pricing_matrices (
    matrix_name, matrix_type, customer_tier, product_category, region, season,
    base_discount_percent, volume_threshold, volume_discount_percent,
    effective_date, expiration_date, is_active
) VALUES 
('Standard Volume Matrix', 'VOLUME', 'STANDARD', NULL, NULL, NULL, 0.0, 50, 5.0, '2024-01-01', '2025-12-31', 1),
('Premium Volume Matrix', 'VOLUME', 'PREMIUM', NULL, NULL, NULL, 5.0, 100, 10.0, '2024-01-01', '2025-12-31', 1),
('Enterprise Volume Matrix', 'VOLUME', 'ENTERPRISE', NULL, NULL, NULL, 10.0, 250, 20.0, '2024-01-01', '2025-12-31', 1),
('Widget Category Matrix', 'PRODUCT_MIX', NULL, 'WIDGETS', NULL, NULL, 2.0, 25, 8.0, '2024-01-01', '2025-12-31', 1),
('Bolt Category Matrix', 'PRODUCT_MIX', NULL, 'BOLTS', NULL, NULL, 1.0, 100, 5.0, '2024-01-01', '2025-12-31', 1);

-- Insert margin targets
INSERT OR REPLACE INTO margin_targets (
    product_code, product_category, customer_tier,
    minimum_margin_percent, target_margin_percent, maximum_discount_percent,
    requires_approval_below, auto_reject_below,
    effective_date, expiration_date, is_active
) VALUES 
-- Product-specific targets
('WIDGET-A100', 'WIDGETS', NULL, 25.0, 35.0, 20.0, 20.0, 15.0, '2024-01-01', '2025-12-31', 1),
('BOLT-M8-50', 'BOLTS', NULL, 15.0, 25.0, 25.0, 12.0, 8.0, '2024-01-01', '2025-12-31', 1),
('GASKET-RUBBER-12', 'GASKETS', NULL, 30.0, 40.0, 15.0, 25.0, 20.0, '2024-01-01', '2025-12-31', 1),

-- Category-specific targets
(NULL, 'WIDGETS', 'STANDARD', 20.0, 30.0, 25.0, 18.0, 12.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'WIDGETS', 'PREMIUM', 25.0, 35.0, 20.0, 22.0, 18.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'WIDGETS', 'ENTERPRISE', 30.0, 40.0, 15.0, 25.0, 20.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'BOLTS', 'STANDARD', 12.0, 20.0, 30.0, 10.0, 5.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'BOLTS', 'PREMIUM', 15.0, 25.0, 25.0, 12.0, 8.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'BOLTS', 'ENTERPRISE', 18.0, 28.0, 20.0, 15.0, 10.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'GASKETS', 'STANDARD', 25.0, 35.0, 20.0, 20.0, 15.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'GASKETS', 'PREMIUM', 30.0, 40.0, 15.0, 25.0, 20.0, '2024-01-01', '2025-12-31', 1),
(NULL, 'GASKETS', 'ENTERPRISE', 35.0, 45.0, 10.0, 30.0, 25.0, '2024-01-01', '2025-12-31', 1);

-- Insert sample pricing approvals (for demonstration)
INSERT OR REPLACE INTO pricing_approvals (
    approval_id, order_reference, customer_id,
    original_total, proposed_total, discount_amount, discount_percent, margin_percent,
    status, approval_level, approved_by, approval_date, rejection_reason,
    requested_by, request_date, business_justification, expiry_date
) VALUES 
('APR-************', 'PO-DEMO-001', 'CUST-001',
 25000.0, 22500.0, 2500.0, 10.0, 28.5,
 'APPROVED', 'MANAGER', 'john.manager', '2024-07-10T14:30:00',
 NULL, 'sales.agent', '2024-07-10T10:15:00',
 'Volume discount for strategic customer', '2024-07-17T10:15:00'),

('APR-************', 'PO-DEMO-002', 'CUST-002',
 15000.0, 13200.0, 1800.0, 12.0, 22.1,
 'PENDING', 'DIRECTOR', NULL, NULL,
 NULL, 'sales.agent', '2024-07-09T16:45:00',
 'Competitive pricing to win large order', '2024-07-16T16:45:00'),

('APR-************', 'PO-DEMO-003', 'CUST-003',
 8000.0, 7200.0, 800.0, 10.0, 18.5,
 'REJECTED', 'MANAGER', 'jane.manager', '2024-07-08T11:20:00',
 'Margin below minimum threshold', 'sales.agent', '2024-07-08T09:30:00',
 'Price match competitor offer', '2024-07-15T09:30:00');
