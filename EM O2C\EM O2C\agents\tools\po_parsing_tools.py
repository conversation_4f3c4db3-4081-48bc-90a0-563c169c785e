#!/usr/bin/env python3
"""
PO Parsing Tools for CrewAI Agent
Tools for reading PO files and saving structured output
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from crewai.tools import tool


class POData(BaseModel):
    """Structured PO data model"""
    po_number: str = Field(description="Purchase order number")
    date: str = Field(description="PO date")
    vendor: Dict[str, str] = Field(description="Vendor information")
    customer: Dict[str, str] = Field(description="Customer/buyer information")
    line_items: List[Dict[str, Any]] = Field(description="List of ordered items")
    pricing: Dict[str, float] = Field(description="Pricing summary")
    delivery: Dict[str, str] = Field(description="Delivery information")
    terms: Dict[str, str] = Field(description="Terms and conditions")
    metadata: Dict[str, str] = Field(description="Additional metadata")


@tool
def po_file_reader(filename: str = "") -> str:
    """Read PO text files from the data/incoming_pos folder

    Args:
        filename: Name of the PO file to read. If empty, lists available files.

    Returns:
        File content or list of available files
    """
    try:
        if not filename:
            # List available files
            po_folder = "data/incoming_pos"
            if not os.path.exists(po_folder):
                return "Error: PO folder not found"

            files = [f for f in os.listdir(po_folder) if f.endswith('.txt')]
            if not files:
                return "No PO files found in incoming folder"

            return f"Available PO files: {', '.join(files)}"

        # Read specific file
        filepath = os.path.join("data/incoming_pos", filename)
        if not os.path.exists(filepath):
            return f"Error: File {filename} not found"

        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        return content

    except Exception as e:
        return f"Error reading file: {str(e)}"


@tool
def po_data_saver(po_data: str, original_filename: str = "") -> str:
    """Save parsed PO data as structured JSON file

    Args:
        po_data: JSON string containing parsed PO data
        original_filename: Original PO filename for reference

    Returns:
        Success message with file path or error message
    """
    try:
        # Create output directories
        parsed_dir = "data/parsed_pos"
        processed_dir = "data/processed_pos"
        os.makedirs(parsed_dir, exist_ok=True)
        os.makedirs(processed_dir, exist_ok=True)

        # Parse the PO data (assuming it's JSON string)
        try:
            parsed_data = json.loads(po_data)
        except json.JSONDecodeError:
            return "Error: Invalid JSON format in PO data"

        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if original_filename:
            base_name = original_filename.replace('.txt', '').replace('PO_', '')
            output_filename = f"PARSED_{base_name}.json"
        else:
            output_filename = f"PARSED_PO_{timestamp}.json"

        output_path = os.path.join(parsed_dir, output_filename)

        # Add metadata
        parsed_data["parsing_metadata"] = {
            "parsed_at": datetime.now().isoformat(),
            "original_file": original_filename,
            "parser_version": "1.0",
            "workflow_stage": "parsed"
        }

        # Save to JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, indent=2, ensure_ascii=False)

        # Move original file to processed folder
        file_management_msg = ""
        if original_filename:
            source_path = os.path.join("data/incoming_pos", original_filename)
            dest_path = os.path.join(processed_dir, original_filename)

            if os.path.exists(source_path):
                shutil.move(source_path, dest_path)
                file_management_msg = f"\n Moved original file: {source_path} → {dest_path}"
            else:
                file_management_msg = f"\n⚠️ Original file not found: {source_path}"

        return f" Successfully saved parsed PO data to: {output_path}{file_management_msg}"

    except Exception as e:
        return f" Error saving PO data: {str(e)}"


@tool
def po_validator(po_data: str) -> str:
    """Validate that parsed PO data contains all required fields

    Args:
        po_data: JSON string containing parsed PO data

    Returns:
        Validation result message
    """
    try:
        # Parse JSON
        try:
            data = json.loads(po_data)
        except json.JSONDecodeError:
            return "Error: Invalid JSON format"

        # Required fields (flexible field names)
        required_fields = [
            "po_number",
            ["date", "po_date"],  # Accept either field name
            "vendor",
            "customer",
            "line_items",
            ["pricing", "pricing_summary"]  # Accept either field name
        ]

        missing_fields = []
        validation_errors = []

        # Check required fields (handle flexible field names)
        for field in required_fields:
            if isinstance(field, list):
                # Check if any of the alternative field names exist
                if not any(f in data for f in field):
                    missing_fields.append(f"{field[0]} (or {'/'.join(field[1:])})")
            else:
                if field not in data:
                    missing_fields.append(field)

        # Validate specific field structures
        if "line_items" in data:
            if not isinstance(data["line_items"], list):
                validation_errors.append("line_items must be a list")
            elif len(data["line_items"]) == 0:
                validation_errors.append("line_items cannot be empty")
            else:
                # Validate line item structure (flexible field names)
                for i, item in enumerate(data["line_items"]):
                    required_item_fields = [
                        ["item_code", "product_code", "sku"],
                        "description",
                        "quantity",
                        ["unit_price", "price"]
                    ]
                    for item_field in required_item_fields:
                        if isinstance(item_field, list):
                            if not any(f in item for f in item_field):
                                validation_errors.append(f"line_items[{i}] missing {item_field[0]} (or {'/'.join(item_field[1:])})")
                        else:
                            if item_field not in item:
                                validation_errors.append(f"line_items[{i}] missing {item_field}")

        # Check pricing (flexible field names)
        pricing_data = data.get("pricing") or data.get("pricing_summary")
        if pricing_data:
            if not isinstance(pricing_data, dict):
                validation_errors.append("pricing/pricing_summary must be a dictionary")
            else:
                required_pricing_fields = [["subtotal", "sub_total"], ["total", "total_amount"]]
                for pricing_field in required_pricing_fields:
                    if isinstance(pricing_field, list):
                        if not any(f in pricing_data for f in pricing_field):
                            validation_errors.append(f"pricing missing {pricing_field[0]} (or {'/'.join(pricing_field[1:])})")
                    else:
                        if pricing_field not in pricing_data:
                            validation_errors.append(f"pricing missing {pricing_field}")
        else:
            validation_errors.append("No pricing or pricing_summary section found")

        # Generate validation report
        if missing_fields or validation_errors:
            report = "Validation Failed:\n"
            if missing_fields:
                report += f"Missing fields: {', '.join(missing_fields)}\n"
            if validation_errors:
                report += f"Validation errors: {'; '.join(validation_errors)}\n"
            return report
        else:
            return "Validation Passed: All required fields present and valid"

    except Exception as e:
        return f"Error during validation: {str(e)}"


@tool
def po_list(folder: str = "data/incoming_pos") -> str:
    """List all available PO files in the incoming folder

    Args:
        folder: Path to the folder containing PO files

    Returns:
        List of available PO files with timestamps
    """
    try:
        if not os.path.exists(folder):
            return f"Error: Folder {folder} not found"

        files = [f for f in os.listdir(folder) if f.endswith('.txt')]

        if not files:
            return f"No PO files found in {folder}"

        # Sort files by modification time (newest first)
        files_with_time = []
        for file in files:
            filepath = os.path.join(folder, file)
            mtime = os.path.getmtime(filepath)
            files_with_time.append((file, mtime))

        files_with_time.sort(key=lambda x: x[1], reverse=True)

        result = f"Found {len(files)} PO files in {folder}:\n"
        for file, mtime in files_with_time:
            mod_time = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            result += f"- {file} (modified: {mod_time})\n"

        return result

    except Exception as e:
        return f"Error listing files: {str(e)}"


# Export tools for CrewAI
def get_po_parsing_tools():
    """Get all PO parsing tools for CrewAI agent"""
    return [po_file_reader, po_data_saver, po_validator, po_list]


if __name__ == "__main__":
    # Test the tools
    print("Testing PO Parsing Tools...")

    tools = get_po_parsing_tools()

    # Test listing files
    list_tool = tools[3]  # po_list
    result = list_tool()
    print("Available PO files:")
    print(result)
