#!/usr/bin/env python3
"""
Simple wrapper script to run the O2C Interactive CLI
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Run the O2C CLI with proper setup"""
    
    # Ensure we're in the right directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("=" * 80)
    print("O2C INTERACTIVE CLI LAUNCHER".center(80))
    print("=" * 80)
    print()
    print("This will start the interactive Order-to-Cash workflow CLI.")
    print("You'll have control over each stage and can see detailed results.")
    print()
    print("Features:")
    print("• Step-by-step workflow execution")
    print("• User approval required for each stage")
    print("• Detailed output display for each step")
    print("• Clean, colored terminal interface")
    print("• Complete workflow from email to invoice")
    print()
    
    # Check if required files exist
    required_files = [
        "simple_o2c_cli.py",
        "services/email_monitor.py",
        "services/workflow_orchestrator.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(" Missing required files:")
        for file_path in missing_files:
            print(f"   • {file_path}")
        print()
        print("Please ensure all O2C components are properly installed.")
        return 1
    
    # Check if data directories exist
    data_dirs = [
        "data/incoming_pos",
        "data/parsed_pos", 
        "data/processed_pos",
        "data/invoices",
        "data/workflow_results"
    ]
    
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print(" Environment check passed")
    print()
    
    # Ask user if they want to continue
    response = input("Start the O2C Interactive CLI? (y/n): ").lower().strip()
    if response not in ['y', 'yes']:
        print("Cancelled by user.")
        return 0
    
    print()
    print(" Starting O2C Interactive CLI...")
    print("=" * 80)
    print()
    
    # Run the CLI
    try:
        result = subprocess.run([sys.executable, "simple_o2c_cli.py"], check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n CLI execution failed with return code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n\n⚠️  CLI interrupted by user (Ctrl+C)")
        return 130
    except Exception as e:
        print(f"\n Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
