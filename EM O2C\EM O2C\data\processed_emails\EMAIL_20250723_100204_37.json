{"email_processing": {"email_id": "37", "processed_at": "2025-07-23T10:02:04.629450", "sender": "366 <PERSON><PERSON><PERSON> <<EMAIL>>", "subject": "SAMPLE PO", "date": "Wed, 23 Jul 2025 00:29:27 +0530", "attachments": ["sample_po.txt"], "has_text_attachments": true, "text_attachment_count": 1}, "po_extraction": {"po_file_created": "PO_20250723_100204_37.txt", "po_file_path": "data/incoming_pos/PO_20250723_100204_37.txt", "extraction_status": "success", "workflow_stage": "extracted"}, "workflow_tracking": {"stage": "email_processed", "next_stage": "awaiting_agent_processing", "created_at": "2025-07-23T10:02:04.629461"}}