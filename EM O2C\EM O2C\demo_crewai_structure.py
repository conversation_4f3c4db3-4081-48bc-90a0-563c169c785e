#!/usr/bin/env python3
"""
Demo script showing CrewAI O2C structure without requiring API key
Shows how the system is organized and what it will do
"""

import os
import json
from agents.tools.po_parsing_tools import get_po_parsing_tools


def show_system_overview():
    """Show the O2C system overview"""
    print(" O2C CrewAI System Overview")
    print("=" * 50)
    
    print("\n📧 STAGE 1: Email Monitoring (COMPLETED)")
    print(" Gmail IMAP integration")
    print(" PO detection with keywords")
    print(" Text attachment extraction")
    print(" Structured file output")
    
    print("\n🤖 STAGE 2: CrewAI PO Parser (READY)")
    print(" DeepSeek LLM integration")
    print(" Intelligent PO parsing agent")
    print(" Structured JSON output")
    print(" Validation and error handling")
    
    print("\n🔮 FUTURE STAGES:")
    print("⏳ Order validation agent")
    print("⏳ Credit management agent")
    print("⏳ Inventory checking agent")
    print("⏳ Pricing and fulfillment agents")


def show_available_tools():
    """Show the PO parsing tools"""
    print("\n🛠️ Available PO Parsing Tools")
    print("=" * 50)
    
    try:
        tools = get_po_parsing_tools()
        tool_descriptions = [
            "Read PO files from incoming folder",
            "Save parsed JSON data to output folder", 
            "Validate parsed data structure",
            "List available PO files with timestamps"
        ]
        
        for i, (tool, desc) in enumerate(zip(tools, tool_descriptions)):
            print(f"{i+1}. {tool.__name__}: {desc}")
        
        return tools
    except Exception as e:
        print(f" Error loading tools: {str(e)}")
        return []


def demo_tools_functionality():
    """Demo the tools functionality"""
    print("\n🧪 Tools Functionality Demo")
    print("=" * 50)
    
    tools = get_po_parsing_tools()
    if not tools:
        return
    
    # Demo po_list tool
    print("\n1.  Listing available PO files:")
    po_list = tools[3]  # po_list function
    result = po_list()
    print(result)
    
    # Demo po_file_reader tool (if files exist)
    if "Found" in result and "PO files" in result:
        print("\n2. 📄 Reading PO file content:")
        po_reader = tools[0]  # po_file_reader function
        
        # Extract first filename from the list
        lines = result.split('\n')
        filename = None
        for line in lines:
            if line.startswith('- ') and '.txt' in line:
                filename = line.split(' ')[1]
                break
        
        if filename:
            print(f"Reading file: {filename}")
            content = po_reader(filename)
            # Show first 500 characters
            if len(content) > 500:
                print(content[:500] + "...")
            else:
                print(content)
        else:
            print("No filename found to read")
    else:
        print("\n2. 📄 No PO files available to read")
    
    # Demo validation tool
    print("\n3.  Validation tool demo:")
    po_validator = tools[2]  # po_validator function
    
    sample_json = json.dumps({
        "po_number": "PO-TEST-001",
        "date": "2024-07-08",
        "vendor": {"name": "Test Vendor"},
        "customer": {"name": "Test Customer"},
        "line_items": [
            {
                "item_code": "ITEM-001",
                "description": "Test Item",
                "quantity": 10,
                "unit_price": 25.0
            }
        ],
        "pricing": {
            "subtotal": 250.0,
            "total": 250.0
        }
    })
    
    validation_result = po_validator(sample_json)
    print(validation_result)


def show_agent_structure():
    """Show the agent structure"""
    print("\n🤖 CrewAI Agent Structure")
    print("=" * 50)
    
    print("\n PO Parser Agent Configuration:")
    print("Role: Senior Purchase Order Parser")
    print("Goal: Extract complete and accurate structured data from PO documents")
    print("LLM: DeepSeek Chat (temperature=0.1 for consistency)")
    print("Tools: 4 specialized PO processing tools")
    print("Memory: Enabled for context retention")
    
    print("\n📝 Agent Task Structure:")
    print("1. Read PO file from incoming folder")
    print("2. Analyze email metadata, body, and attachments")
    print("3. Extract structured data (vendor, customer, line items, pricing)")
    print("4. Validate extracted data completeness")
    print("5. Save as structured JSON file")
    
    print("\n🎯 Expected JSON Output Structure:")
    sample_structure = {
        "po_number": "string",
        "date": "YYYY-MM-DD",
        "vendor": {
            "name": "string",
            "address": {"street": "...", "city": "...", "state": "...", "zip": "..."},
            "contact": {"phone": "...", "email": "...", "person": "..."}
        },
        "customer": {
            "name": "string",
            "billing_address": {"...": "..."},
            "shipping_address": {"...": "..."}
        },
        "line_items": [
            {
                "item_code": "string",
                "description": "string", 
                "quantity": "number",
                "unit_price": "number",
                "line_total": "number"
            }
        ],
        "pricing": {
            "subtotal": "number",
            "tax_amount": "number",
            "shipping": "number",
            "total": "number"
        },
        "delivery": {"address": "...", "requested_date": "..."},
        "terms": {"payment_terms": "...", "conditions": "..."}
    }
    
    print(json.dumps(sample_structure, indent=2))


def show_next_steps():
    """Show next steps to run the system"""
    print("\n Next Steps to Run the System")
    print("=" * 50)
    
    print("\n1. 🔑 Set up DeepSeek API Key:")
    print("   - Get API key from: https://platform.deepseek.com/api_keys")
    print("   - Run: python3 set_deepseek_key.py")
    print("   - Or set manually: export DEEPSEEK_API_KEY='your-key'")
    
    print("\n2. 🧪 Test the complete setup:")
    print("   python3 test_crewai_setup.py")
    
    print("\n3. 🤖 Run PO parsing:")
    print("   python3 o2c_crew.py                    # Parse latest PO")
    print("   python3 o2c_crew.py filename.txt       # Parse specific file")
    print("   python3 o2c_crew.py --all              # Parse all pending POs")
    
    print("\n4. 📊 Check results:")
    print("   ls -la data/parsed_pos/                # View parsed JSON files")
    print("   cat data/parsed_pos/PARSED_*.json      # View parsed content")
    
    print("\n5. 📧 Add more PO emails:")
    print("   - Send PO emails to: <EMAIL>")
    print("   - Run: python3 run_email_monitor.py --check")
    print("   - Then parse with CrewAI agents")


def main():
    """Main demo function"""
    print("🎭 CrewAI O2C System Structure Demo")
    print("This demo shows the system structure without requiring API keys\n")
    
    show_system_overview()
    show_available_tools()
    demo_tools_functionality()
    show_agent_structure()
    show_next_steps()
    
    print("\n" + "=" * 50)
    print("🎉 Demo Complete!")
    print("The CrewAI O2C system is ready for DeepSeek API integration!")
    print("=" * 50)


if __name__ == "__main__":
    main()
