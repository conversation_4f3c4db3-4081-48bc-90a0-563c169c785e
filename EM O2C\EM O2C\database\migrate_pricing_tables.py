#!/usr/bin/env python3
"""
Migrate Pricing & Contract Management Tables to O2C Database

This script:
1. Creates the new pricing and contract management tables
2. Adds indexes and triggers for performance
3. Inserts sample pricing data
"""

import os
import sys
import sqlite3
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def migrate_pricing_tables():
    """Migrate pricing tables to the database"""
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), "o2c_master_data.db")
    
    print("🔧 Starting Pricing & Contract Management Tables Migration...")
    print(f"📁 Database: {db_path}")
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(" Connected to database")
        
        # Read and execute schema updates
        schema_file = os.path.join(os.path.dirname(__file__), "schema.sql")
        
        print(" Reading schema file...")
        with open(schema_file, 'r') as f:
            schema_sql = f.read()
        
        # Split schema into individual statements
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        print(f"🔨 Executing {len(statements)} schema statements...")
        
        # Execute each statement
        for i, statement in enumerate(statements):
            try:
                if statement.strip():
                    cursor.execute(statement)
                    if i % 10 == 0:  # Progress indicator
                        print(f"   Processed {i+1}/{len(statements)} statements...")
            except sqlite3.Error as e:
                # Skip errors for existing tables/indexes
                if "already exists" not in str(e).lower():
                    print(f"⚠️  Warning executing statement {i+1}: {e}")
        
        print(" Schema migration completed")
        
        # Insert sample pricing data
        print("📊 Inserting sample pricing data...")
        
        pricing_data_file = os.path.join(os.path.dirname(__file__), "pricing_sample_data.sql")
        
        with open(pricing_data_file, 'r') as f:
            pricing_sql = f.read()
        
        # Split pricing data into individual statements
        pricing_statements = [stmt.strip() for stmt in pricing_sql.split(';') if stmt.strip()]
        
        print(f"📥 Executing {len(pricing_statements)} data insertion statements...")
        
        # Execute each pricing statement
        for i, statement in enumerate(pricing_statements):
            try:
                if statement.strip():
                    cursor.execute(statement)
            except sqlite3.Error as e:
                print(f"⚠️  Warning executing pricing statement {i+1}: {e}")
        
        print(" Sample pricing data inserted")
        
        # Commit changes
        conn.commit()
        
        # Verify tables exist
        print(" Verifying pricing tables...")
        
        pricing_tables = [
            'customer_contracts',
            'contract_pricing_rules', 
            'pricing_matrices',
            'margin_targets',
            'pricing_approvals'
        ]
        
        for table in pricing_tables:
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if cursor.fetchone()[0] > 0:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    {table}: {count} records")
            else:
                print(f"    {table}: Table not found")
        
        print("\n🎉 Pricing & Contract Management Tables Migration Completed Successfully!")
        print("=" * 70)
        print("📊 Summary:")
        print("    Customer contracts table created")
        print("    Contract pricing rules table created")
        print("    Pricing matrices table created")
        print("    Margin targets table created")
        print("    Pricing approvals table created")
        print("    Indexes and triggers added")
        print("    Sample data inserted")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f" Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()
            print("🔒 Database connection closed")


def verify_pricing_migration():
    """Verify the pricing migration was successful"""
    
    db_path = os.path.join(os.path.dirname(__file__), "o2c_master_data.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n Verification Report:")
        print("=" * 50)
        
        # Check customer contracts
        cursor.execute("SELECT COUNT(*) FROM customer_contracts WHERE status = 'ACTIVE'")
        active_contracts = cursor.fetchone()[0]
        print(f" Active Customer Contracts: {active_contracts}")
        
        # Check pricing rules
        cursor.execute("SELECT COUNT(*) FROM contract_pricing_rules WHERE is_active = 1")
        active_rules = cursor.fetchone()[0]
        print(f"📏 Active Pricing Rules: {active_rules}")
        
        # Check pricing matrices
        cursor.execute("SELECT COUNT(*) FROM pricing_matrices WHERE is_active = 1")
        active_matrices = cursor.fetchone()[0]
        print(f"📊 Active Pricing Matrices: {active_matrices}")
        
        # Check margin targets
        cursor.execute("SELECT COUNT(*) FROM margin_targets WHERE is_active = 1")
        active_targets = cursor.fetchone()[0]
        print(f"🎯 Active Margin Targets: {active_targets}")
        
        # Check pricing approvals
        cursor.execute("SELECT COUNT(*) FROM pricing_approvals")
        total_approvals = cursor.fetchone()[0]
        print(f"🔐 Pricing Approvals: {total_approvals}")
        
        # Sample contract details
        print("\n Sample Contract Details:")
        cursor.execute("""
            SELECT contract_id, customer_id, contract_type, base_discount_percent, payment_terms
            FROM customer_contracts 
            WHERE status = 'ACTIVE' 
            LIMIT 3
        """)
        
        contracts = cursor.fetchall()
        for contract in contracts:
            print(f"   • {contract[0]}: {contract[1]} ({contract[2]}) - {contract[3]}% discount, {contract[4]}")
        
        print("=" * 50)
        print(" Verification completed successfully!")
        
        return True
        
    except Exception as e:
        print(f" Verification failed: {str(e)}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()


def main():
    """Main migration function"""
    print(" O2C Pricing & Contract Management Migration")
    print("=" * 70)
    
    # Run migration
    success = migrate_pricing_tables()
    
    if success:
        # Run verification
        verify_pricing_migration()
        print("\n🎉 Migration completed successfully!")
        print("💰 Dynamic Pricing & Contract Management system is ready!")
    else:
        print("\n Migration failed. Please check the errors above.")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
