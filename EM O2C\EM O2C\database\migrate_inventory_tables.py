#!/usr/bin/env python3
"""
Database Migration Script for Inventory Tables
Adds inventory management tables to existing O2C database
"""

import sqlite3
import os
from datetime import datetime


def migrate_inventory_tables(db_path: str = "database/o2c_master_data.db"):
    """Add inventory tables to existing database"""
    print("🔄 Migrating database to add inventory tables...")
    
    if not os.path.exists(db_path):
        print(f" Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if inventory tables already exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_levels'")
        if cursor.fetchone():
            print(" Inventory tables already exist")
            conn.close()
            return True
        
        print("📦 Creating inventory tables...")
        
        # Create inventory_levels table
        cursor.execute("""
            CREATE TABLE inventory_levels (
                inventory_id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code TEXT NOT NULL,
                warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
                quantity_on_hand INTEGER NOT NULL DEFAULT 0,
                quantity_available INTEGER NOT NULL DEFAULT 0,
                quantity_allocated INTEGER NOT NULL DEFAULT 0,
                quantity_on_order INTEGER NOT NULL DEFAULT 0,
                reorder_point INTEGER NOT NULL DEFAULT 0,
                reorder_quantity INTEGER NOT NULL DEFAULT 0,
                safety_stock INTEGER NOT NULL DEFAULT 0,
                last_count_date TEXT,
                last_movement_date TEXT,
                created_date TEXT NOT NULL DEFAULT (datetime('now')),
                updated_date TEXT NOT NULL DEFAULT (datetime('now')),
                
                FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE,
                UNIQUE(product_code, warehouse_location)
            )
        """)
        
        # Create stock_allocations table
        cursor.execute("""
            CREATE TABLE stock_allocations (
                allocation_id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code TEXT NOT NULL,
                warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
                order_reference TEXT NOT NULL,
                allocated_quantity INTEGER NOT NULL,
                allocation_date TEXT NOT NULL DEFAULT (datetime('now')),
                expiry_date TEXT,
                status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'FULFILLED', 'EXPIRED', 'CANCELLED')) DEFAULT 'ACTIVE',
                created_by TEXT,
                created_date TEXT NOT NULL DEFAULT (datetime('now')),
                
                FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
            )
        """)
        
        # Create supplier_lead_times table
        cursor.execute("""
            CREATE TABLE supplier_lead_times (
                lead_time_id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code TEXT NOT NULL,
                supplier_name TEXT NOT NULL,
                supplier_code TEXT,
                lead_time_days INTEGER NOT NULL,
                minimum_order_quantity INTEGER NOT NULL DEFAULT 1,
                price_per_unit REAL,
                is_preferred BOOLEAN NOT NULL DEFAULT 0,
                effective_date TEXT NOT NULL DEFAULT (datetime('now')),
                end_date TEXT,
                created_date TEXT NOT NULL DEFAULT (datetime('now')),
                
                FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
            )
        """)
        
        # Create backorders table
        cursor.execute("""
            CREATE TABLE backorders (
                backorder_id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_reference TEXT NOT NULL,
                product_code TEXT NOT NULL,
                customer_id TEXT NOT NULL,
                requested_quantity INTEGER NOT NULL,
                backordered_quantity INTEGER NOT NULL,
                priority_level TEXT NOT NULL CHECK (priority_level IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')) DEFAULT 'MEDIUM',
                expected_availability_date TEXT,
                customer_notification_sent BOOLEAN NOT NULL DEFAULT 0,
                status TEXT NOT NULL CHECK (status IN ('PENDING', 'PARTIAL', 'FULFILLED', 'CANCELLED')) DEFAULT 'PENDING',
                created_date TEXT NOT NULL DEFAULT (datetime('now')),
                updated_date TEXT NOT NULL DEFAULT (datetime('now')),
                
                FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE,
                FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
            )
        """)
        
        # Create inventory_transactions table
        cursor.execute("""
            CREATE TABLE inventory_transactions (
                transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code TEXT NOT NULL,
                warehouse_location TEXT NOT NULL DEFAULT 'MAIN',
                transaction_type TEXT NOT NULL CHECK (transaction_type IN ('RECEIPT', 'SHIPMENT', 'ADJUSTMENT', 'ALLOCATION', 'DEALLOCATION', 'TRANSFER')),
                quantity_change INTEGER NOT NULL,
                reference_number TEXT,
                order_reference TEXT,
                reason_code TEXT,
                notes TEXT,
                transaction_date TEXT NOT NULL DEFAULT (datetime('now')),
                created_by TEXT,
                
                FOREIGN KEY (product_code) REFERENCES products(product_code) ON DELETE CASCADE
            )
        """)
        
        print("📊 Creating indexes...")
        
        # Create indexes
        indexes = [
            "CREATE INDEX idx_inventory_product ON inventory_levels(product_code)",
            "CREATE INDEX idx_inventory_warehouse ON inventory_levels(warehouse_location)",
            "CREATE INDEX idx_inventory_available ON inventory_levels(quantity_available)",
            "CREATE INDEX idx_allocations_product ON stock_allocations(product_code)",
            "CREATE INDEX idx_allocations_order ON stock_allocations(order_reference)",
            "CREATE INDEX idx_allocations_status ON stock_allocations(status)",
            "CREATE INDEX idx_leadtime_product ON supplier_lead_times(product_code)",
            "CREATE INDEX idx_leadtime_supplier ON supplier_lead_times(supplier_name)",
            "CREATE INDEX idx_backorders_product ON backorders(product_code)",
            "CREATE INDEX idx_backorders_customer ON backorders(customer_id)",
            "CREATE INDEX idx_transactions_product ON inventory_transactions(product_code)",
            "CREATE INDEX idx_transactions_type ON inventory_transactions(transaction_type)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("🔧 Creating triggers...")
        
        # Create triggers
        cursor.execute("""
            CREATE TRIGGER update_inventory_levels_timestamp 
                AFTER UPDATE ON inventory_levels
                FOR EACH ROW
            BEGIN
                UPDATE inventory_levels SET updated_date = datetime('now') WHERE inventory_id = NEW.inventory_id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER update_backorders_timestamp 
                AFTER UPDATE ON backorders
                FOR EACH ROW
            BEGIN
                UPDATE backorders SET updated_date = datetime('now') WHERE backorder_id = NEW.backorder_id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER update_available_quantity_on_allocation
                AFTER INSERT ON stock_allocations
                FOR EACH ROW
                WHEN NEW.status = 'ACTIVE'
            BEGIN
                UPDATE inventory_levels 
                SET quantity_allocated = quantity_allocated + NEW.allocated_quantity,
                    quantity_available = quantity_on_hand - (quantity_allocated + NEW.allocated_quantity)
                WHERE product_code = NEW.product_code AND warehouse_location = NEW.warehouse_location;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER update_available_quantity_on_deallocation
                AFTER UPDATE ON stock_allocations
                FOR EACH ROW
                WHEN OLD.status = 'ACTIVE' AND NEW.status IN ('FULFILLED', 'EXPIRED', 'CANCELLED')
            BEGIN
                UPDATE inventory_levels 
                SET quantity_allocated = quantity_allocated - OLD.allocated_quantity,
                    quantity_available = quantity_on_hand - (quantity_allocated - OLD.allocated_quantity)
                WHERE product_code = OLD.product_code AND warehouse_location = OLD.warehouse_location;
            END
        """)
        
        print("📦 Populating sample inventory data...")
        
        # Populate sample data
        inventory_data = [
            ('WIDGET-A100', 'MAIN', 500, 450, 50, 100, 100, 200, 50),
            ('BOLT-M8-50', 'MAIN', 2000, 1800, 200, 500, 500, 1000, 200),
            ('GASKET-RUBBER-12', 'MAIN', 0, 0, 0, 0, 50, 100, 25)
        ]
        
        for data in inventory_data:
            cursor.execute("""
                INSERT INTO inventory_levels 
                (product_code, warehouse_location, quantity_on_hand, quantity_available, 
                 quantity_allocated, quantity_on_order, reorder_point, reorder_quantity, safety_stock,
                 last_count_date, last_movement_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, date('now', '-7 days'), date('now', '-1 day'))
            """, data)
        
        # Populate supplier lead times
        supplier_data = [
            ('WIDGET-A100', 'Premium Widgets Inc', 'SUP-001', 10, 50, 120.00, 1),
            ('WIDGET-A100', 'Alternative Widget Co', 'SUP-002', 14, 25, 130.00, 0),
            ('BOLT-M8-50', 'Fastener Solutions Ltd', 'SUP-003', 7, 500, 2.25, 1),
            ('GASKET-RUBBER-12', 'Rubber Components Corp', 'SUP-004', 21, 100, 5.50, 1)
        ]
        
        for data in supplier_data:
            cursor.execute("""
                INSERT INTO supplier_lead_times 
                (product_code, supplier_name, supplier_code, lead_time_days, 
                 minimum_order_quantity, price_per_unit, is_preferred)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, data)
        
        # Add sample inventory transactions
        transaction_data = [
            ('WIDGET-A100', 'MAIN', 'RECEIPT', 100, 'PO-2024-001', 'PURCHASE_ORDER', 'Weekly delivery'),
            ('BOLT-M8-50', 'MAIN', 'RECEIPT', 500, 'PO-2024-002', 'PURCHASE_ORDER', 'Bulk order'),
            ('WIDGET-A100', 'MAIN', 'SHIPMENT', -25, 'SO-2024-001', 'SALES_ORDER', 'Customer shipment'),
            ('BOLT-M8-50', 'MAIN', 'ADJUSTMENT', -50, 'ADJ-001', 'CYCLE_COUNT', 'Count variance')
        ]
        
        for data in transaction_data:
            cursor.execute("""
                INSERT INTO inventory_transactions 
                (product_code, warehouse_location, transaction_type, quantity_change,
                 reference_number, reason_code, notes, created_by, transaction_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'SYSTEM', date('now', '-' || (ABS(RANDOM()) % 30) || ' days'))
            """, data)
        
        conn.commit()
        conn.close()
        
        print(" Inventory tables migration completed successfully!")
        return True
        
    except Exception as e:
        print(f" Migration failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = migrate_inventory_tables()
    if success:
        print("🎉 Database migration completed!")
    else:
        print("💥 Database migration failed!")
