#!/usr/bin/env python3
"""
SQLite Master Data Database Setup
Initializes O2C master data database and populates with current mock data
"""

import sqlite3
import os
import json
from pathlib import Path
from datetime import datetime


class O2CMasterDataSetup:
    """Setup and initialize O2C master data SQLite database"""
    
    def __init__(self, db_path: str = "database/o2c_master_data.db"):
        """Initialize database setup"""
        self.db_path = db_path
        self.schema_path = "database/schema.sql"
        
        # Ensure database directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    def create_database(self):
        """Create database and tables from schema"""
        print(f"🔧 Creating SQLite database: {self.db_path}")
        
        # Read schema file
        if not os.path.exists(self.schema_path):
            raise FileNotFoundError(f"Schema file not found: {self.schema_path}")
        
        with open(self.schema_path, 'r') as f:
            schema_sql = f.read()
        
        # Create database and execute schema
        with sqlite3.connect(self.db_path) as conn:
            conn.executescript(schema_sql)
            conn.commit()
        
        print(" Database and tables created successfully")
    
    def populate_customers(self):
        """Populate customers table with mock data"""
        print("📊 Populating customers table...")
        
        customers_data = [
            {
                'customer_id': 'CUST-001',
                'name': 'ABC Manufacturing Corp',
                'status': 'ACTIVE',
                'credit_limit': 50000.00,
                'payment_terms': 'Net 30',
                'risk_score': 'LOW',
                'tax_exempt': False,
                'currency': 'USD',
                'email': '<EMAIL>',
                'phone': '(*************',
                'annual_revenue': 25000000,
                'employees': 150,
                'duns_number': '*********',
                'credit_score': 785,
                'credit_rating': 'A-',
                'established_date': '2018-03-15',
                'industry': 'Manufacturing',
                'current_balance': 12500.00,
                'available_credit': 37500.00,
                'last_credit_review': '2024-06-01',
                'financial_strength': 'Strong',
                'industry_risk': 'Medium',
                'geographic_risk': 'Low',
                'concentration_risk': 'Low'
            },
            {
                'customer_id': 'CUST-002',
                'name': 'Test Supplier Inc.',
                'status': 'ACTIVE',
                'credit_limit': 25000.00,
                'payment_terms': 'Net 15',
                'risk_score': 'MEDIUM',
                'tax_exempt': True,
                'currency': 'USD',
                'email': '<EMAIL>',
                'phone': '(*************',
                'annual_revenue': 5000000,
                'employees': 75,
                'credit_score': 720,
                'credit_rating': 'B+',
                'established_date': '2020-01-10',
                'industry': 'Retail',
                'current_balance': 8500.00,
                'available_credit': 16500.00,
                'last_credit_review': '2024-05-15',
                'financial_strength': 'Good',
                'industry_risk': 'Medium',
                'geographic_risk': 'Low',
                'concentration_risk': 'Medium'
            },
            {
                'customer_id': 'CUST-003',
                'name': 'Premium Manufacturing Inc',
                'status': 'ACTIVE',
                'credit_limit': 100000.00,
                'payment_terms': 'Net 30',
                'risk_score': 'LOW',
                'tax_exempt': False,
                'currency': 'USD',
                'email': '<EMAIL>',
                'phone': '(*************',
                'annual_revenue': 50000000,
                'employees': 300,
                'credit_score': 850,
                'credit_rating': 'AAA',
                'established_date': '2015-06-20',
                'industry': 'Manufacturing',
                'current_balance': 15000.00,
                'available_credit': 85000.00,
                'last_credit_review': '2024-07-01',
                'financial_strength': 'Very Strong',
                'industry_risk': 'Low',
                'geographic_risk': 'Low',
                'concentration_risk': 'Low'
            }
        ]
        
        with sqlite3.connect(self.db_path) as conn:
            for customer in customers_data:
                conn.execute("""
                    INSERT INTO customers (
                        customer_id, name, status, credit_limit, payment_terms, risk_score,
                        tax_exempt, currency, email, phone, annual_revenue, employees,
                        duns_number, credit_score, credit_rating, established_date, industry,
                        current_balance, available_credit, last_credit_review, financial_strength,
                        industry_risk, geographic_risk, concentration_risk
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    customer['customer_id'], customer['name'], customer['status'],
                    customer['credit_limit'], customer['payment_terms'], customer['risk_score'],
                    customer['tax_exempt'], customer['currency'], customer['email'],
                    customer['phone'], customer['annual_revenue'], customer['employees'],
                    customer.get('duns_number'), customer['credit_score'], customer['credit_rating'],
                    customer['established_date'], customer['industry'], customer['current_balance'],
                    customer['available_credit'], customer['last_credit_review'],
                    customer['financial_strength'], customer['industry_risk'],
                    customer['geographic_risk'], customer['concentration_risk']
                ))
            conn.commit()
        
        print(f" Inserted {len(customers_data)} customers")
    
    def populate_customer_addresses(self):
        """Populate customer addresses"""
        print("🏠 Populating customer addresses...")
        
        addresses_data = [
            {
                'customer_id': 'CUST-001',
                'address_type': 'BILLING',
                'street': '123 Business Street',
                'city': 'Anytown',
                'state': 'CA',
                'zip_code': '90210',
                'is_primary': True
            },
            {
                'customer_id': 'CUST-001',
                'address_type': 'SHIPPING',
                'street': '456 Industrial Blvd',
                'city': 'Anytown',
                'state': 'CA',
                'zip_code': '90211',
                'is_primary': False
            },
            {
                'customer_id': 'CUST-002',
                'address_type': 'BOTH',
                'street': '789 Vendor Avenue',
                'city': 'Supplier City',
                'state': 'TX',
                'zip_code': '75001',
                'is_primary': True
            },
            {
                'customer_id': 'CUST-003',
                'address_type': 'BILLING',
                'street': '100 Excellence Blvd',
                'city': 'Success City',
                'state': 'CA',
                'zip_code': '90210',
                'is_primary': True
            },
            {
                'customer_id': 'CUST-003',
                'address_type': 'SHIPPING',
                'street': '200 Quality Drive',
                'city': 'Success City',
                'state': 'CA',
                'zip_code': '90211',
                'is_primary': False
            }
        ]
        
        with sqlite3.connect(self.db_path) as conn:
            for address in addresses_data:
                conn.execute("""
                    INSERT INTO customer_addresses (
                        customer_id, address_type, street, city, state, zip_code, is_primary
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    address['customer_id'], address['address_type'], address['street'],
                    address['city'], address['state'], address['zip_code'], address['is_primary']
                ))
            conn.commit()
        
        print(f" Inserted {len(addresses_data)} addresses")
    
    def populate_products(self):
        """Populate products table with mock data"""
        print("📦 Populating products table...")
        
        products_data = [
            {
                'product_code': 'WIDGET-A100',
                'description': 'Premium Widget Assembly Type A',
                'status': 'ACTIVE',
                'unit_price': 125.00,
                'category': 'WIDGETS',
                'tax_code': 'TAXABLE',
                'minimum_order': 10,
                'maximum_discount': 15.0,
                'weight': 2.5,
                'dimensions': '10x8x6 inches',
                'manufacturer': 'Widget Corp',
                'lead_time_days': 7
            },
            {
                'product_code': 'BOLT-M8-50',
                'description': 'M8 x 50mm Stainless Steel Bolts',
                'status': 'ACTIVE',
                'unit_price': 2.50,
                'category': 'HARDWARE',
                'tax_code': 'TAXABLE',
                'minimum_order': 50,
                'maximum_discount': 10.0,
                'weight': 0.1,
                'dimensions': '8mm x 50mm',
                'manufacturer': 'Steel Works Inc',
                'lead_time_days': 3
            },
            {
                'product_code': 'GASKET-RUBBER-12',
                'description': '12mm Rubber Gasket',
                'status': 'DISCONTINUED',
                'unit_price': 5.75,
                'category': 'SEALS',
                'tax_code': 'TAXABLE',
                'minimum_order': 25,
                'maximum_discount': 5.0,
                'weight': 0.05,
                'dimensions': '12mm diameter',
                'manufacturer': 'Seal Solutions',
                'lead_time_days': 14
            }
        ]
        
        with sqlite3.connect(self.db_path) as conn:
            for product in products_data:
                conn.execute("""
                    INSERT INTO products (
                        product_code, description, status, unit_price, category, tax_code,
                        minimum_order, maximum_discount, weight, dimensions, manufacturer, lead_time_days
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    product['product_code'], product['description'], product['status'],
                    product['unit_price'], product['category'], product['tax_code'],
                    product['minimum_order'], product['maximum_discount'], product['weight'],
                    product['dimensions'], product['manufacturer'], product['lead_time_days']
                ))
            conn.commit()
        
        print(f" Inserted {len(products_data)} products")

    def populate_business_rules(self):
        """Populate business rules table"""
        print(" Populating business rules...")

        rules_data = [
            {
                'rule_name': 'minimum_order_value',
                'rule_category': 'ORDER_LIMITS',
                'rule_value': '100.00',
                'rule_type': 'NUMERIC',
                'description': 'Minimum order value in USD'
            },
            {
                'rule_name': 'maximum_order_value',
                'rule_category': 'ORDER_LIMITS',
                'rule_value': '100000.00',
                'rule_type': 'NUMERIC',
                'description': 'Maximum order value in USD'
            },
            {
                'rule_name': 'delivery_lead_time_days',
                'rule_category': 'DELIVERY',
                'rule_value': '14',
                'rule_type': 'NUMERIC',
                'description': 'Standard delivery lead time in days'
            },
            {
                'rule_name': 'maximum_line_items',
                'rule_category': 'ORDER_LIMITS',
                'rule_value': '50',
                'rule_type': 'NUMERIC',
                'description': 'Maximum number of line items per order'
            },
            {
                'rule_name': 'supported_currencies',
                'rule_category': 'CURRENCY',
                'rule_value': '["USD", "EUR", "CAD"]',
                'rule_type': 'JSON',
                'description': 'List of supported currencies'
            },
            {
                'rule_name': 'required_fields',
                'rule_category': 'VALIDATION',
                'rule_value': '["po_number", "customer", "line_items", "pricing_summary"]',
                'rule_type': 'JSON',
                'description': 'Required fields for PO validation'
            }
        ]

        with sqlite3.connect(self.db_path) as conn:
            for rule in rules_data:
                conn.execute("""
                    INSERT INTO business_rules (
                        rule_name, rule_category, rule_value, rule_type, description
                    ) VALUES (?, ?, ?, ?, ?)
                """, (
                    rule['rule_name'], rule['rule_category'], rule['rule_value'],
                    rule['rule_type'], rule['description']
                ))
            conn.commit()

        print(f" Inserted {len(rules_data)} business rules")

    def populate_tax_rates(self):
        """Populate tax rates table"""
        print("💰 Populating tax rates...")

        tax_data = [
            {'state_code': 'CA', 'tax_rate': 8.25, 'effective_date': '2024-01-01'},
            {'state_code': 'TX', 'tax_rate': 6.25, 'effective_date': '2024-01-01'},
            {'state_code': 'NY', 'tax_rate': 8.0, 'effective_date': '2024-01-01'},
            {'state_code': 'FL', 'tax_rate': 6.0, 'effective_date': '2024-01-01'},
            {'state_code': 'WA', 'tax_rate': 6.5, 'effective_date': '2024-01-01'}
        ]

        with sqlite3.connect(self.db_path) as conn:
            for tax in tax_data:
                conn.execute("""
                    INSERT INTO tax_rates (state_code, tax_rate, effective_date)
                    VALUES (?, ?, ?)
                """, (tax['state_code'], tax['tax_rate'], tax['effective_date']))
            conn.commit()

        print(f" Inserted {len(tax_data)} tax rates")

    def populate_inventory_data(self):
        """Populate inventory-related tables with sample data"""
        print("📦 Populating inventory tables...")

        with sqlite3.connect(self.db_path) as conn:
            # Populate inventory levels
            inventory_data = [
                ('WIDGET-A100', 'MAIN', 500, 450, 50, 100, 100, 200, 50),
                ('BOLT-M8-50', 'MAIN', 2000, 1800, 200, 500, 500, 1000, 200),
                ('GASKET-RUBBER-12', 'MAIN', 0, 0, 0, 0, 50, 100, 25)  # Discontinued item
            ]

            for data in inventory_data:
                conn.execute("""
                    INSERT INTO inventory_levels
                    (product_code, warehouse_location, quantity_on_hand, quantity_available,
                     quantity_allocated, quantity_on_order, reorder_point, reorder_quantity, safety_stock,
                     last_count_date, last_movement_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, date('now', '-7 days'), date('now', '-1 day'))
                """, data)

            # Populate supplier lead times
            supplier_data = [
                ('WIDGET-A100', 'Premium Widgets Inc', 'SUP-001', 10, 50, 120.00, 1),
                ('WIDGET-A100', 'Alternative Widget Co', 'SUP-002', 14, 25, 130.00, 0),
                ('BOLT-M8-50', 'Fastener Solutions Ltd', 'SUP-003', 7, 500, 2.25, 1),
                ('GASKET-RUBBER-12', 'Rubber Components Corp', 'SUP-004', 21, 100, 5.50, 1)
            ]

            for data in supplier_data:
                conn.execute("""
                    INSERT INTO supplier_lead_times
                    (product_code, supplier_name, supplier_code, lead_time_days,
                     minimum_order_quantity, price_per_unit, is_preferred)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, data)

            # Add some sample inventory transactions
            transaction_data = [
                ('WIDGET-A100', 'MAIN', 'RECEIPT', 100, 'PO-2024-001', 'PURCHASE_ORDER', 'Weekly delivery'),
                ('BOLT-M8-50', 'MAIN', 'RECEIPT', 500, 'PO-2024-002', 'PURCHASE_ORDER', 'Bulk order'),
                ('WIDGET-A100', 'MAIN', 'SHIPMENT', -25, 'SO-2024-001', 'SALES_ORDER', 'Customer shipment'),
                ('BOLT-M8-50', 'MAIN', 'ADJUSTMENT', -50, 'ADJ-001', 'CYCLE_COUNT', 'Count variance')
            ]

            for data in transaction_data:
                conn.execute("""
                    INSERT INTO inventory_transactions
                    (product_code, warehouse_location, transaction_type, quantity_change,
                     reference_number, reason_code, notes, created_by, transaction_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'SYSTEM', date('now', '-' || (ABS(RANDOM()) % 30) || ' days'))
                """, data)

            conn.commit()

        print(" Inventory data populated successfully")

    def setup_complete_database(self):
        """Complete database setup process"""
        print(" Starting O2C Master Data Database Setup")
        print("=" * 50)

        try:
            # Create database and schema
            self.create_database()

            # Populate all tables
            self.populate_customers()
            self.populate_customer_addresses()
            self.populate_products()
            self.populate_business_rules()
            self.populate_tax_rates()
            self.populate_inventory_data()

            print("\n" + "=" * 50)
            print("🎉 Database setup completed successfully!")
            print(f"📍 Database location: {os.path.abspath(self.db_path)}")

            # Show summary
            self.show_database_summary()

        except Exception as e:
            print(f" Database setup failed: {str(e)}")
            raise

    def show_database_summary(self):
        """Show database summary"""
        print("\n📊 Database Summary:")
        print("-" * 30)

        with sqlite3.connect(self.db_path) as conn:
            tables = [
                ('customers', 'Customers'),
                ('customer_addresses', 'Customer Addresses'),
                ('products', 'Products'),
                ('business_rules', 'Business Rules'),
                ('tax_rates', 'Tax Rates'),
                ('inventory_levels', 'Inventory Levels'),
                ('supplier_lead_times', 'Supplier Lead Times'),
                ('inventory_transactions', 'Inventory Transactions')
            ]

            for table_name, display_name in tables:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  {display_name}: {count} records")


def main():
    """Main setup function"""
    setup = O2CMasterDataSetup()
    setup.setup_complete_database()


if __name__ == "__main__":
    main()
