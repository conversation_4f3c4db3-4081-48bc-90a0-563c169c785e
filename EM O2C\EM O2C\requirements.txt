# Core dependencies for O2C (Order-to-Cash) Agentic System
# Python 3.8+ required

# ===== EMAIL PROCESSING =====
# Email monitoring and processing
imaplib2==3.6
email-validator==2.0.0

# ===== DATA PROCESSING & VALIDATION =====
# Data models and validation
pydantic==2.5.0
python-dateutil==2.8.2

# ===== CREWAI & LLM DEPENDENCIES =====
# CrewAI framework for multi-agent orchestration
crewai==0.70.1

# LangChain for LLM integration
langchain==0.3.7
langchain-openai==0.2.8
openai==1.54.4

# ===== HTTP & API REQUESTS =====
# For DeepSeek API integration and general HTTP requests
requests==2.32.3

# ===== DATABASE =====
# SQLite is built into Python, but these help with advanced operations
# No additional dependencies needed for basic SQLite operations

# ===== FILE PROCESSING =====
# Enhanced file operations and path handling
pathlib2==2.3.7; python_version < "3.4"

# ===== EMAIL SENDING (SMTP) =====
# Built-in smtplib is sufficient for basic email sending
# email.mime modules are built-in

# ===== CONFIGURATION & ENVIRONMENT =====
# Environment variables and configuration
python-dotenv==1.0.0

# ===== LOGGING =====
# Enhanced logging capabilities
python-json-logger==2.0.7

# ===== TESTING =====
# Testing framework and utilities
pytest==7.4.3
pytest-mock==3.12.0

# ===== OPTIONAL ENHANCEMENTS =====
# Uncomment if you need these features:

# For better JSON handling and performance
# ujson==5.8.0

# For advanced file watching (if implementing real-time monitoring)
# watchdog==3.0.0

# For PDF processing (if POs come as PDFs)
# PyPDF2==3.0.1
# pdfplumber==0.9.0

# For Excel processing (if POs come as Excel files)
# openpyxl==3.1.2
# pandas==2.1.4

# For advanced email parsing
# email-reply-parser==0.5.12

# For better datetime handling
# arrow==1.3.0
