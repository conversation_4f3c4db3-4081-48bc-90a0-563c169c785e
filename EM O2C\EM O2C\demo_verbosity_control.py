#!/usr/bin/env python3
"""
Demo script showing verbosity control in action
"""

import os
from manage_verbosity import (
    verbosity_manager,
    set_global_verbose,
    disable_all_verbosity,
    get_agent_verbose,
    get_crew_verbose
)

def demo_verbosity_control():
    """Demonstrate verbosity control functionality"""
    
    print("🎭 Verbosity Control System Demo")
    print("=" * 60)
    
    print("\n Current Configuration:")
    verbosity_manager.print_current_settings()
    
    print("\n🔧 Testing Verbosity Changes:")
    print("-" * 40)
    
    # Test 1: Check individual agent settings
    print("\n1️⃣ Individual Agent Verbosity Check:")
    agents = [
        "po_parser_agent",
        "order_validation_agent", 
        "credit_management_agent",
        "inventory_management_agent",
        "pricing_management_agent",
        "fulfillment_coordination_agent",
        "invoice_generation_agent"
    ]
    
    for agent in agents:
        verbose = get_agent_verbose(agent)
        print(f"   {agent}: {verbose}")
    
    # Test 2: Enable global verbosity
    print("\n2️⃣ Enabling Global Verbosity:")
    set_global_verbose(True)
    print("   Global verbosity enabled")
    
    for agent in agents:
        verbose = get_agent_verbose(agent)
        print(f"   {agent}: {verbose}")
    
    crew_verbose = get_crew_verbose()
    print(f"   crew_verbose: {crew_verbose}")
    
    # Test 3: Set individual agent verbosity
    print("\n3️⃣ Setting Individual Agent Verbosity:")
    verbosity_manager.set_agent_verbose("po_parser_agent", False)
    print("   Set po_parser_agent to False (should override global)")
    
    for agent in agents:
        verbose = get_agent_verbose(agent)
        status = "" if verbose else ""
        print(f"   {agent}: {verbose} {status}")
    
    # Test 4: Disable all verbosity
    print("\n4️⃣ Disabling All Verbosity:")
    disable_all_verbosity()
    print("   All verbosity disabled")
    
    for agent in agents:
        verbose = get_agent_verbose(agent)
        print(f"   {agent}: {verbose}")
    
    crew_verbose = get_crew_verbose()
    print(f"   crew_verbose: {crew_verbose}")
    
    print("\n Demo completed successfully!")
    
    print("\n📚 Key Features Demonstrated:")
    print("    Global verbosity control")
    print("    Individual agent settings")
    print("    Crew verbosity control")
    print("    Configuration persistence")
    print("    Command-line interface")

def demo_cli_commands():
    """Show CLI command examples"""
    
    print("\n🖥️  Command Line Interface Examples:")
    print("=" * 60)
    
    commands = [
        ("Show current settings", "python3 manage_verbosity.py --show"),
        ("Disable all verbosity", "python3 manage_verbosity.py --disable-all"),
        ("Enable global verbosity", "python3 manage_verbosity.py --global-verbose True"),
        ("Disable global verbosity", "python3 manage_verbosity.py --global-verbose False"),
        ("Set agent verbosity", "python3 manage_verbosity.py --agent po_parser_agent --agent-verbose True"),
        ("Disable agent verbosity", "python3 manage_verbosity.py --agent invoice_generation_agent --agent-verbose False"),
    ]
    
    for description, command in commands:
        print(f"\n📌 {description}:")
        print(f"   {command}")

def demo_production_usage():
    """Show production usage recommendations"""
    
    print("\n🏭 Production Usage Recommendations:")
    print("=" * 60)
    
    print("\n For Clean Production Logs:")
    print("   python3 manage_verbosity.py --disable-all")
    print("   → All agents run silently")
    print("   → Clean CLI output")
    print("   → Professional appearance")
    
    print("\n🐛 For Debugging Issues:")
    print("   python3 manage_verbosity.py --global-verbose True")
    print("   → All agents show detailed logs")
    print("   → Full execution traces")
    print("   → Tool input/output visible")
    
    print("\n🎯 For Specific Agent Debugging:")
    print("   python3 manage_verbosity.py --disable-all")
    print("   python3 manage_verbosity.py --agent po_parser_agent --agent-verbose True")
    print("   → Only PO Parser shows verbose output")
    print("   → Focused debugging")
    
    print("\n📊 For Development:")
    print("   python3 manage_verbosity.py --global-verbose True")
    print("   → See all agent interactions")
    print("   → Understand workflow execution")
    print("   → Monitor tool usage")

if __name__ == "__main__":
    print("🎉 O2C Verbosity Control System")
    print("=" * 80)
    
    # Run the demo
    demo_verbosity_control()
    
    # Show CLI examples
    demo_cli_commands()
    
    # Show production usage
    demo_production_usage()
    
    print("\n🎊 Verbosity Control System Successfully Implemented!")
    print("=" * 80)
    
    print("\n Summary of Benefits:")
    print("    Single point of control for all agent verbosity")
    print("    Clean production logs by default")
    print("    Easy debugging when needed")
    print("    Individual agent control")
    print("    Command-line management")
    print("    Configuration persistence")
    print("    No code changes required")
    
    print("\n Your O2C system now has professional verbosity management!")
