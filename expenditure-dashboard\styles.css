/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* Header Styles */
.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.company-logo {
    height: 40px;
    width: auto;
}

.header-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.header-subtitle {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 500;
}

/* Executive Summary */
.executive-summary {
    padding: 2rem 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.summary-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.summary-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.card-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.card-subtitle {
    font-size: 0.9rem;
    color: #a0aec0;
    font-weight: 500;
}

.summary-card.highlight .card-icon {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.summary-card.success .card-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.summary-card.roi .card-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.summary-card.payback .card-icon {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

/* Main Dashboard */
.dashboard-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px 24px 0 0;
    margin-top: 2rem;
    min-height: calc(100vh - 200px);
}

/* Section Headers */
.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 500;
}

/* Toggle Buttons */
.toggle-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.toggle-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #e2e8f0;
    background: white;
    color: #718096;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.toggle-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

/* Breakdown Views */
.breakdown-container {
    position: relative;
    min-height: 600px;
}

.breakdown-view {
    display: none;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.breakdown-view.active {
    display: grid;
}

.cost-chart-container {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.cost-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cost-category {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f7fafc;
}

.category-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
}

.category-total {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
}

.cost-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.cost-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.cost-item:hover {
    background: #edf2f7;
}

.item-label {
    font-weight: 500;
    color: #4a5568;
}

.item-value {
    font-weight: 700;
    color: #2d3748;
    text-align: right;
}

.item-per-order {
    font-size: 0.9rem;
    color: #718096;
    text-align: right;
}

/* Comparison Table */
.comparison-table {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-top: 2rem;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.comparison-table td {
    font-weight: 500;
    color: #2d3748;
}

.savings {
    color: #38a169;
    font-weight: 700;
}

.improvement {
    color: #3182ce;
    font-weight: 700;
}

.comparison-chart-container {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

/* ROI Analysis */
.roi-analysis {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 2px solid #e2e8f0;
}

.roi-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: start;
}

.roi-metrics {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.metric-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.metric-content {
    flex: 1;
}

.metric-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.metric-subtitle {
    font-size: 0.85rem;
    color: #a0aec0;
}

.roi-chart-container {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

/* Scalability Analysis */
.scalability-analysis {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 2px solid #e2e8f0;
}

.scalability-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.scalability-chart {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.scalability-table {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.scalability-table table {
    width: 100%;
    border-collapse: collapse;
}

.scalability-table th,
.scalability-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.9rem;
}

.scalability-table th:first-child,
.scalability-table td:first-child {
    text-align: left;
}

.scalability-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.current-volume {
    background: #f0fff4;
    border-left: 4px solid #38a169;
}

/* Additional Value */
.additional-value {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 2px solid #e2e8f0;
}

.value-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.value-category {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.value-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f7fafc;
}

.value-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.value-total {
    font-size: 1.4rem;
    font-weight: 700;
    color: #38a169;
}

.value-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.value-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 10px;
    transition: background-color 0.3s ease;
}

.value-item:hover {
    background: #edf2f7;
}

.item-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.total-economic-impact {
    display: flex;
    justify-content: center;
}

.impact-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 3rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    max-width: 600px;
    width: 100%;
}

.impact-icon {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.2);
}

.impact-content {
    flex: 1;
}

.impact-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.impact-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.impact-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .breakdown-view {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .roi-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .scalability-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .value-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .dashboard-main {
        padding: 1rem;
    }

    .summary-container {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }

    .header-content {
        padding: 0 1rem;
    }

    .header-title {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .header-title h1 {
        font-size: 1.5rem;
    }

    .toggle-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cost-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: center;
    }

    .comparison-table {
        overflow-x: auto;
    }

    .impact-card {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }
}
