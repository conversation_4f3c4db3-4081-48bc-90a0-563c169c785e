#!/usr/bin/env python3
"""
Simple O2C Interactive CLI Interface
Provides step-by-step control over the complete Order-to-Cash workflow
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import logging suppression FIRST to prevent verbose output
import suppress_llm_logging

# Import O2C components
try:
    from services.email_monitor import EmailMonitor
    from services.workflow_orchestrator import O2CWorkflowOrchestrator
except ImportError as e:
    print(f"Error importing O2C components: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


class SimpleO2CCLI:
    """Simple Interactive CLI for O2C Workflow Management"""
    
    def __init__(self):
        """Initialize CLI interface"""
        self.email_monitor = None
        self.workflow_orchestrator = O2CWorkflowOrchestrator()
        
        # CLI styling
        self.HEADER = '\033[95m'
        self.BLUE = '\033[94m'
        self.CYAN = '\033[96m'
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BOLD = '\033[1m'
        self.END = '\033[0m'
        
    def print_header(self, text: str):
        """Print styled header"""
        print(f"\n{self.HEADER}{self.BOLD}{'='*80}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{text.center(80)}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{'='*80}{self.END}\n")
        
    def print_stage(self, stage_num: int, stage_name: str):
        """Print stage header"""
        print(f"\n{self.CYAN}{self.BOLD}Stage {stage_num}: {stage_name}{self.END}")
        print(f"{self.CYAN}{'─'*60}{self.END}")
        
    def print_success(self, text: str):
        """Print success message"""
        print(f"{self.GREEN}✓ {text}{self.END}")
        
    def print_warning(self, text: str):
        """Print warning message"""
        print(f"{self.YELLOW}⚠ {text}{self.END}")
        
    def print_error(self, text: str):
        """Print error message"""
        print(f"{self.RED}✗ {text}{self.END}")
        
    def print_info(self, text: str):
        """Print info message"""
        print(f"{self.BLUE}ℹ {text}{self.END}")
        
    def get_user_approval(self, prompt: str = "Continue?") -> bool:
        """Get user approval to proceed"""
        while True:
            response = input(f"\n{self.YELLOW}► {prompt} (y/n/q): {self.END}").lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            elif response in ['q', 'quit']:
                print(f"\n{self.RED}Workflow terminated by user.{self.END}")
                sys.exit(0)
            else:
                print(f"{self.RED}Please enter 'y' for yes, 'n' for no, or 'q' to quit.{self.END}")
    
    def run_email_monitoring(self) -> str:
        """Run email monitoring to get PO file"""
        self.print_stage(0, "Email Monitoring")

        print("Checking for new purchase orders...")
        print("Note: Email monitoring will NOT auto-trigger the workflow in CLI mode.")
        print("You will have full control over each step.")
        print()
        print("Options:")
        print("1. Check email for new POs")
        print("2. Use existing PO file")
        print("3. Create test PO")
        
        while True:
            choice = input(f"\n{self.YELLOW}Select option (1-3): {self.END}").strip()
            if choice == "1":
                return self.check_email_for_pos()
            elif choice == "2":
                return self.select_existing_po()
            elif choice == "3":
                return self.create_test_po()
            else:
                print(f"{self.RED}Please enter 1, 2, or 3.{self.END}")
    
    def check_email_for_pos(self) -> str:
        """Check email for new POs without auto-triggering workflow"""
        try:
            # Create email monitor with CLI mode (no auto-trigger)
            self.email_monitor = EmailMonitor()
            print(f"\n{self.BLUE}Checking email for new POs...{self.END}")

            # Temporarily disable automated workflow to prevent auto-trigger
            original_config = self.email_monitor.config.copy()
            if "processing" not in self.email_monitor.config:
                self.email_monitor.config["processing"] = {}
            self.email_monitor.config["processing"]["automated_workflow"] = False

            result = self.email_monitor.run_monitoring_cycle()

            # Restore original config
            self.email_monitor.config = original_config

            if result and result.get('new_pos_found', 0) > 0:
                po_files = result.get('po_files', [])
                self.print_success(f"Found {len(po_files)} new PO(s)")

                if len(po_files) == 1:
                    return po_files[0]
                else:
                    print(f"\n{self.YELLOW}Multiple POs found:{self.END}")
                    for i, po_file in enumerate(po_files, 1):
                        print(f"  {i}. {po_file}")

                    while True:
                        try:
                            choice = int(input(f"\n{self.YELLOW}Select PO (1-{len(po_files)}): {self.END}"))
                            if 1 <= choice <= len(po_files):
                                return po_files[choice - 1]
                        except ValueError:
                            pass
                        print(f"{self.RED}Invalid choice.{self.END}")
            else:
                self.print_warning("No new POs found in email")
                return self.select_existing_po()

        except Exception as e:
            self.print_error(f"Email check failed: {str(e)}")
            return self.select_existing_po()
    
    def select_existing_po(self) -> str:
        """Select existing PO file"""
        incoming_dir = Path("data/incoming_pos")
        po_files = list(incoming_dir.glob("*.txt")) if incoming_dir.exists() else []
        
        if not po_files:
            self.print_warning("No PO files found")
            if self.get_user_approval("Create test PO?"):
                return self.create_test_po()
            return ""
        
        print(f"\n{self.BLUE}Available PO files:{self.END}")
        for i, po_file in enumerate(po_files, 1):
            print(f"  {i}. {po_file.name}")
        
        while True:
            try:
                choice = int(input(f"\n{self.YELLOW}Select PO (1-{len(po_files)}): {self.END}"))
                if 1 <= choice <= len(po_files):
                    return po_files[choice - 1].name
            except ValueError:
                pass
            print(f"{self.RED}Invalid choice.{self.END}")
    
    def create_test_po(self) -> str:
        """Create a test PO file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        po_number = f"PO-CLI-TEST-{timestamp}"
        filename = f"PO_{timestamp}.txt"
        
        test_po_content = f"""=== EMAIL METADATA ===
Email ID: CLI-TEST-{timestamp}
Timestamp: {datetime.now().isoformat()}
Sender: <EMAIL>
Subject: Purchase Order {po_number}

=== EMAIL BODY ===
Purchase Order: {po_number}
Customer: Test Customer Corp
Line Items:
- WIDGET-A100: 100 x $125.00
- BOLT-M8-50: 200 x $2.50
Total: $13000.00
Delivery Date: 2025-08-01
Payment Terms: Net 30

=== TEXT ATTACHMENTS ===
ATTACHMENT: purchase_order_details.txt

PURCHASE ORDER
==============
PO Number: {po_number}
Date: {datetime.now().strftime('%Y-%m-%d')}

VENDOR:
Your Company Name
123 Vendor Street
Business City, ST 12345

CUSTOMER:
Test Customer Corp
123 Business Street
Anytown, CA 90210

SHIP TO:
456 Industrial Blvd
Anytown, CA 90211

LINE ITEMS:
1. WIDGET-A100 - Premium Widget Assembly Type A
   Quantity: 100
   Unit Price: $125.00
   Line Total: $12500.00

2. BOLT-M8-50 - M8 x 50mm Stainless Steel Bolts
   Quantity: 200
   Unit Price: $2.50
   Line Total: $500.00

PRICING SUMMARY:
Subtotal: $13000.00
Tax (8.0%): $1040.00
Shipping: $100.00
TOTAL: $14140.00

DELIVERY:
Requested Date: 2025-08-01
Address: 456 Industrial Blvd, Anytown, CA 90211

TERMS:
Payment Terms: Net 30
Currency: USD

Authorized by: Test User
Contact: <EMAIL>
Phone: 555-0123
"""
        
        # Create directories and file
        os.makedirs("data/incoming_pos", exist_ok=True)
        po_path = Path("data/incoming_pos") / filename
        with open(po_path, 'w') as f:
            f.write(test_po_content)
        
        self.print_success(f"Created test PO: {filename}")
        return filename
    
    def display_workflow_results(self, result: dict):
        """Display workflow results in a clean format"""
        print(f"\n{self.GREEN}{self.BOLD}Workflow Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")
        
        # Overall status
        overall_status = result.get('overall_status', 'UNKNOWN')
        status_color = self.GREEN if overall_status in ['INVOICE_SENT', 'READY_FOR_SHIPMENT'] else self.YELLOW
        print(f"{self.CYAN}Overall Status:{self.END} {status_color}{overall_status}{self.END}")
        
        # Stages
        stages = result.get('stages', {})
        stage_names = [
            ('po_parsing', 'PO Parsing'),
            ('order_validation', 'Order Validation'),
            ('credit_assessment', 'Credit Assessment'),
            ('inventory_assessment', 'Inventory Assessment'),
            ('pricing_assessment', 'Pricing Assessment'),
            ('fulfillment_coordination', 'Fulfillment Coordination'),
            ('invoice_generation', 'Invoice Generation')
        ]
        
        print(f"\n{self.CYAN}Stage Results:{self.END}")
        for stage_key, stage_name in stage_names:
            stage_data = stages.get(stage_key, {})
            status = stage_data.get('status', 'NOT_RUN')
            
            if status == 'SUCCESS':
                print(f"  {self.GREEN}✓{self.END} {stage_name}: {self.GREEN}{status}{self.END}")
            elif status == 'ERROR':
                print(f"  {self.RED}✗{self.END} {stage_name}: {self.RED}{status}{self.END}")
            elif status == 'NOT_RUN':
                print(f"  {self.YELLOW}○{self.END} {stage_name}: {self.YELLOW}{status}{self.END}")
            else:
                print(f"  {self.YELLOW}⚠{self.END} {stage_name}: {self.YELLOW}{status}{self.END}")
        
        # Key metrics
        if 'po_parsing' in stages and stages['po_parsing'].get('status') == 'SUCCESS':
            parsed_file = stages['po_parsing'].get('parsed_file', '')
            if parsed_file:
                try:
                    with open(f"data/parsed_pos/{parsed_file}", 'r') as f:
                        parsed_data = json.load(f)
                    
                    print(f"\n{self.CYAN}Order Details:{self.END}")
                    print(f"  PO Number: {parsed_data.get('po_number', 'N/A')}")
                    print(f"  Customer: {parsed_data.get('customer', {}).get('name', 'N/A')}")
                    print(f"  Total: ${parsed_data.get('pricing', {}).get('total', 0):,.2f}")
                    print(f"  Items: {len(parsed_data.get('line_items', []))}")
                except:
                    pass
    
    def run_stage_by_stage_workflow(self, po_filename: str):
        """Run workflow stage by stage with user approval for each"""

        # Show workflow stages
        print(f"\n{self.BLUE}{self.BOLD}Interactive Stage-by-Stage Workflow:{self.END}")
        print("You will be asked to approve each stage before execution.")
        print()
        print("Stages:")
        print("1. PO Parsing - Extract structured data")
        print("2. Order Validation - Comprehensive validation")
        print("3. Credit Assessment - Customer credit check")
        print("4. Inventory Assessment - Stock availability")
        print("5. Pricing Assessment - Dynamic pricing")
        print("6. Fulfillment Coordination - Shipping setup")
        print("7. Invoice Generation - Create and email invoice")

        if not self.get_user_approval("Start stage-by-stage execution?"):
            return

        po_file_path = f"data/incoming_pos/{po_filename}"
        workflow_data = {}

        try:
            # Stage 1: PO Parsing
            if self.run_stage_1_parsing(po_file_path, workflow_data):
                # Stage 2: Order Validation
                if self.run_stage_2_validation(workflow_data):
                    # Stage 3: Credit Assessment
                    if self.run_stage_3_credit(workflow_data):
                        # Stage 4: Inventory Assessment
                        if self.run_stage_4_inventory(workflow_data):
                            # Stage 5: Pricing Assessment
                            if self.run_stage_5_pricing(workflow_data):
                                # Stage 6: Fulfillment Coordination
                                if self.run_stage_6_fulfillment(workflow_data):
                                    # Stage 7: Invoice Generation
                                    if self.run_stage_7_invoice(workflow_data):
                                        self.display_final_success(workflow_data)
                                    else:
                                        self.print_error("Workflow stopped at Stage 7: Invoice Generation")
                                else:
                                    self.print_error("Workflow stopped at Stage 6: Fulfillment Coordination")
                            else:
                                self.print_error("Workflow stopped at Stage 5: Pricing Assessment")
                        else:
                            self.print_error("Workflow stopped at Stage 4: Inventory Assessment")
                    else:
                        self.print_error("Workflow stopped at Stage 3: Credit Assessment")
                else:
                    self.print_error("Workflow stopped at Stage 2: Order Validation")
            else:
                self.print_error("Workflow stopped at Stage 1: PO Parsing")

        except Exception as e:
            self.print_error(f"Workflow execution failed: {str(e)}")

    def run_stage_1_parsing(self, po_file_path: str, workflow_data: dict) -> bool:
        """Run Stage 1: PO Parsing with user approval"""
        self.print_stage(1, "PO Parsing")

        print("This stage will extract structured data from the purchase order file.")
        print("The PO will be parsed to extract customer info, line items, pricing, etc.")

        if not self.get_user_approval("Run PO Parsing stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Parsing purchase order...{self.END}")
            result = self.workflow_orchestrator._run_po_parser(po_file_path)

            if result and result.get('status') == 'SUCCESS':
                self.print_success("PO parsing completed successfully!")

                # Load and display parsed data
                parsed_file = result.get('parsed_file', '')
                if parsed_file:
                    try:
                        with open(f"data/parsed_pos/{parsed_file}", 'r') as f:
                            parsed_data = json.load(f)
                        self.display_parsed_data_summary(parsed_data)
                        workflow_data['parsed_file'] = parsed_file
                        workflow_data['parsed_data'] = parsed_data
                    except Exception as e:
                        self.print_warning(f"Could not load parsed data: {e}")

                return True
            else:
                self.print_error(f"PO parsing failed: {result.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            self.print_error(f"PO parsing error: {str(e)}")
            return False

    def run_stage_2_validation(self, workflow_data: dict) -> bool:
        """Run Stage 2: Order Validation with user approval"""
        self.print_stage(2, "Order Validation")

        print("This stage will perform comprehensive validation of the parsed order.")
        print("Includes customer verification, product checks, and business rules.")

        if not self.get_user_approval("Run Order Validation stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running order validation...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            result = self.workflow_orchestrator._run_order_validation(parsed_file)

            if result:
                self.display_validation_summary(result)
                workflow_data['validation_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status in ['SUCCESS', 'REVIEW']:
                    self.print_success(f"Order validation completed: {status}")
                    return True
                else:
                    self.print_error(f"Order validation failed: {status}")
                    return False
            else:
                self.print_error("Order validation failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Order validation error: {str(e)}")
            return False

    def run_stage_3_credit(self, workflow_data: dict) -> bool:
        """Run Stage 3: Credit Assessment with user approval"""
        self.print_stage(3, "Credit Assessment")

        print("This stage will assess customer creditworthiness and payment risk.")
        print("Credit limits, payment history, and risk factors will be evaluated.")

        if not self.get_user_approval("Run Credit Assessment stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running credit assessment...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            validation_result = workflow_data.get('validation_result', {})

            result = self.workflow_orchestrator._run_credit_assessment(parsed_file, validation_result)

            if result:
                self.display_credit_summary(result)
                workflow_data['credit_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status == 'SUCCESS':
                    self.print_success("Credit assessment completed successfully!")
                    return True
                else:
                    self.print_error(f"Credit assessment failed: {status}")
                    return False
            else:
                self.print_error("Credit assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Credit assessment error: {str(e)}")
            return False

    def run_stage_4_inventory(self, workflow_data: dict) -> bool:
        """Run Stage 4: Inventory Assessment with user approval"""
        self.print_stage(4, "Inventory Assessment")

        print("This stage will check product availability and plan fulfillment.")
        print("Stock levels, lead times, and fulfillment options will be evaluated.")

        if not self.get_user_approval("Run Inventory Assessment stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running inventory assessment...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            validation_result = workflow_data.get('validation_result', {})
            credit_result = workflow_data.get('credit_result', {})

            result = self.workflow_orchestrator._run_inventory_assessment(parsed_file, validation_result, credit_result)

            if result:
                self.display_inventory_summary(result)
                workflow_data['inventory_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status == 'SUCCESS':
                    self.print_success("Inventory assessment completed successfully!")
                    return True
                else:
                    self.print_error(f"Inventory assessment failed: {status}")
                    return False
            else:
                self.print_error("Inventory assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Inventory assessment error: {str(e)}")
            return False

    def run_stage_5_pricing(self, workflow_data: dict) -> bool:
        """Run Stage 5: Pricing Assessment with user approval"""
        self.print_stage(5, "Pricing Assessment")

        print("This stage will calculate dynamic pricing and apply contract terms.")
        print("Base pricing, discounts, and contract conditions will be evaluated.")

        if not self.get_user_approval("Run Pricing Assessment stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running pricing assessment...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            inventory_result = workflow_data.get('inventory_result', {})

            result = self.workflow_orchestrator._run_pricing_assessment(parsed_file, inventory_result)

            if result:
                self.display_pricing_summary(result)
                workflow_data['pricing_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status == 'SUCCESS':
                    self.print_success("Pricing assessment completed successfully!")
                    return True
                else:
                    self.print_error(f"Pricing assessment failed: {status}")
                    return False
            else:
                self.print_error("Pricing assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Pricing assessment error: {str(e)}")
            return False

    def run_stage_6_fulfillment(self, workflow_data: dict) -> bool:
        """Run Stage 6: Fulfillment Coordination with user approval"""
        self.print_stage(6, "Fulfillment Coordination")

        print("This stage will coordinate warehouse operations and shipping.")
        print("Pick lists, shipping carriers, and delivery tracking will be set up.")

        if not self.get_user_approval("Run Fulfillment Coordination stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running fulfillment coordination...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            inventory_result = workflow_data.get('inventory_result', {})
            pricing_result = workflow_data.get('pricing_result', {})

            result = self.workflow_orchestrator._run_fulfillment_coordination(parsed_file, inventory_result, pricing_result)

            if result:
                self.display_fulfillment_summary(result)
                workflow_data['fulfillment_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status == 'SUCCESS':
                    self.print_success("Fulfillment coordination completed successfully!")
                    return True
                else:
                    self.print_error(f"Fulfillment coordination failed: {status}")
                    return False
            else:
                self.print_error("Fulfillment coordination failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Fulfillment coordination error: {str(e)}")
            return False

    def run_stage_7_invoice(self, workflow_data: dict) -> bool:
        """Run Stage 7: Invoice Generation with user approval"""
        self.print_stage(7, "Invoice Generation & Email Distribution")

        print("This stage will generate a professional invoice and email it to the customer.")
        print("Payment terms, contact information, and delivery confirmation will be included.")

        if not self.get_user_approval("Run Invoice Generation stage?"):
            return False

        try:
            print(f"\n{self.BLUE}Running invoice generation...{self.END}")
            parsed_file = workflow_data.get('parsed_file', '')
            fulfillment_result = workflow_data.get('fulfillment_result', {})

            result = self.workflow_orchestrator._run_invoice_generation(parsed_file, fulfillment_result)

            if result:
                self.display_invoice_summary(result)
                workflow_data['invoice_result'] = result

                status = result.get('status', 'UNKNOWN')
                if status == 'SUCCESS':
                    self.print_success("Invoice generation completed successfully!")
                    return True
                else:
                    self.print_error(f"Invoice generation failed: {status}")
                    return False
            else:
                self.print_error("Invoice generation failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Invoice generation error: {str(e)}")
            return False

    def display_parsed_data_summary(self, parsed_data: dict):
        """Display summary of parsed PO data"""
        print(f"\n{self.GREEN}{self.BOLD}Parsed PO Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        po_number = parsed_data.get('po_number', 'N/A')
        customer_name = parsed_data.get('customer', {}).get('name', 'N/A')
        total = parsed_data.get('pricing', {}).get('total', 0)
        items_count = len(parsed_data.get('line_items', []))

        print(f"{self.CYAN}PO Number:{self.END} {po_number}")
        print(f"{self.CYAN}Customer:{self.END} {customer_name}")
        print(f"{self.CYAN}Total Amount:{self.END} ${total:,.2f}")
        print(f"{self.CYAN}Line Items:{self.END} {items_count} items")

    def display_validation_summary(self, result: dict):
        """Display validation results summary"""
        print(f"\n{self.GREEN}{self.BOLD}Validation Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        status_color = self.GREEN if status == 'SUCCESS' else self.YELLOW
        print(f"{self.CYAN}Status:{self.END} {status_color}{status}{self.END}")

        # Show validation status
        validation_status = result.get('validation_status', 'UNKNOWN')
        print(f"{self.CYAN}Validation Result:{self.END} {validation_status}")

        # Extract detailed validation information from agent result
        agent_result = result.get('agent_result', '')
        if agent_result:
            print(f"\n{self.CYAN}Validation Details:{self.END}")

            # Try to extract structured validation information
            self._extract_validation_details(agent_result)

            # Also show risk assessment if available
            if 'Risk Score:' in agent_result or 'Risk Level:' in agent_result:
                print(f"\n{self.CYAN}Risk Assessment:{self.END}")
                lines = str(agent_result).split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['Risk Score:', 'Risk Level:', 'Overall Risk:']):
                        print(f"  • {line.strip()}")

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_validation_details(self, agent_result: str):
        """Extract and display detailed validation information"""
        try:
            # Look for validation categories and their results
            validation_categories = [
                ('Customer Validation', ['customer', 'Customer']),
                ('Product Validation', ['product', 'Product', 'catalog']),
                ('Business Rules', ['business', 'Business', 'rules']),
                ('Delivery Validation', ['delivery', 'Delivery', 'address']),
                ('Compliance Check', ['compliance', 'Compliance', 'regulatory'])
            ]

            lines = str(agent_result).split('\n')
            current_category = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Check if this line indicates a validation category
                for category, keywords in validation_categories:
                    if any(keyword in line for keyword in keywords) and ('validation' in line.lower() or 'check' in line.lower()):
                        current_category = category
                        break

                # Extract meaningful validation results
                if any(indicator in line for indicator in ['✓', '✗', 'PASS', 'FAIL', 'SUCCESS', 'ERROR', 'Valid', 'Invalid']):
                    # Skip empty or too generic lines
                    if line.strip() in ['- Status: PASS', '- Status: FAIL', '- Status: SUCCESS', '- Status: ERROR']:
                        continue

                    # Clean up the line and show it
                    clean_line = line.replace('✓', '').replace('✗', '').strip()

                    # Only show if it has meaningful content
                    if len(clean_line) > 15 and any(word in clean_line.lower() for word in ['customer', 'product', 'credit', 'delivery', 'compliance', 'price', 'order', 'validation']):
                        if 'PASS' in line or 'SUCCESS' in line or '✓' in line:
                            print(f"  ✅ {clean_line}")
                        elif 'FAIL' in line or 'ERROR' in line or '✗' in line:
                            print(f"  ❌ {clean_line}")
                        else:
                            print(f"  • {clean_line}")

                # Extract specific validation messages with more context
                elif any(keyword in line.lower() for keyword in ['validated', 'verified', 'checked', 'found', 'exists', 'available', 'approved', 'denied']):
                    if len(line) > 15 and len(line) < 120:  # Reasonable length with more context
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            shown_lines = 0
            for line in lines:
                if any(keyword in line for keyword in ['PASS', 'FAIL', 'SUCCESS', 'ERROR', 'Valid', 'Invalid']):
                    print(f"  • {line.strip()}")
                    shown_lines += 1
                    if shown_lines >= 8:  # Show more details
                        break

    def display_credit_summary(self, result: dict):
        """Display credit assessment summary"""
        print(f"\n{self.GREEN}{self.BOLD}Credit Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}{status}{self.END}")

        # Extract detailed credit information from result
        agent_result = result.get('agent_result', '') or result.get('credit_assessment', '')
        if agent_result:
            print(f"\n{self.CYAN}Credit Assessment Details:{self.END}")
            self._extract_credit_details(agent_result)

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_credit_details(self, agent_result: str):
        """Extract and display detailed credit assessment information"""
        try:
            lines = str(agent_result).split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for credit-specific information
                if any(keyword in line for keyword in [
                    'Credit Decision:', 'Credit Limit:', 'Risk Level:', 'Risk Score:',
                    'Payment History:', 'Credit Rating:', 'Approved', 'Denied',
                    'Available Credit:', 'Outstanding Balance:', 'Payment Terms:'
                ]):
                    if 'Approved' in line or 'APPROVED' in line:
                        print(f"   {line}")
                    elif 'Denied' in line or 'DENIED' in line or 'REJECTED' in line:
                        print(f"   {line}")
                    else:
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Credit', 'Risk', 'Limit', 'Decision']):
                    print(f"  • {line.strip()}")

    def display_inventory_summary(self, result: dict):
        """Display inventory assessment summary"""
        print(f"\n{self.GREEN}{self.BOLD}Inventory Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}{status}{self.END}")

        # Extract detailed inventory information from result
        agent_result = result.get('agent_result', '') or result.get('inventory_assessment', '')
        if agent_result:
            print(f"\n{self.CYAN}Inventory Assessment Details:{self.END}")
            self._extract_inventory_details(agent_result)

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_inventory_details(self, agent_result: str):
        """Extract and display detailed inventory assessment information"""
        try:
            lines = str(agent_result).split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for inventory-specific information
                if any(keyword in line for keyword in [
                    'Fulfillment Status:', 'Lead Time:', 'Availability:', 'Stock Level:',
                    'Available:', 'In Stock:', 'Backorder:', 'Warehouse:', 'SKU:',
                    'Product:', 'Quantity:', 'Reserved:', 'On Hand:'
                ]):
                    if 'Available' in line or 'In Stock' in line or 'AVAILABLE' in line:
                        print(f"  ✅ {line}")
                    elif 'Backorder' in line or 'Out of Stock' in line or 'UNAVAILABLE' in line:
                        print(f"  ⚠️ {line}")
                    else:
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Inventory', 'Stock', 'Available', 'Fulfillment']):
                    print(f"  • {line.strip()}")

    def display_pricing_summary(self, result: dict):
        """Display pricing assessment summary"""
        print(f"\n{self.GREEN}{self.BOLD}Pricing Assessment Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}{status}{self.END}")

        # Extract detailed pricing information from result
        agent_result = result.get('agent_result', '') or result.get('pricing_assessment', '')
        if agent_result:
            print(f"\n{self.CYAN}Pricing Assessment Details:{self.END}")
            self._extract_pricing_details(agent_result)

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_pricing_details(self, agent_result: str):
        """Extract and display detailed pricing assessment information"""
        try:
            lines = str(agent_result).split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for pricing-specific information
                if any(keyword in line for keyword in [
                    'Final Total:', 'Discount:', 'Pricing Status:', 'Base Price:',
                    'Unit Price:', 'Total Amount:', 'Contract Price:', 'List Price:',
                    'Subtotal:', 'Tax:', 'Shipping:', 'Net Price:'
                ]):
                    if '$' in line or 'Total' in line or 'Price' in line:
                        print(f"  💰 {line}")
                    else:
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Price', 'Total', 'Discount', 'Cost']):
                    print(f"  • {line.strip()}")

    def display_fulfillment_summary(self, result: dict):
        """Display fulfillment coordination summary"""
        print(f"\n{self.GREEN}{self.BOLD}Fulfillment Coordination Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}{status}{self.END}")

        # Extract detailed fulfillment information from result
        agent_result = result.get('agent_result', '') or result.get('fulfillment_coordination', '')
        if agent_result:
            print(f"\n{self.CYAN}Fulfillment Coordination Details:{self.END}")
            self._extract_fulfillment_details(agent_result)

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_fulfillment_details(self, agent_result: str):
        """Extract and display detailed fulfillment coordination information"""
        try:
            lines = str(agent_result).split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for fulfillment-specific information
                if any(keyword in line for keyword in [
                    'Warehouse:', 'Shipping:', 'Carrier:', 'Delivery:', 'Pick List:',
                    'Tracking:', 'Estimated Delivery:', 'Ship Date:', 'Fulfillment Status:',
                    'Location:', 'Route:', 'Package:', 'Logistics:'
                ]):
                    if 'Confirmed' in line or 'Scheduled' in line or 'Ready' in line:
                        print(f"  🚚 {line}")
                    else:
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Warehouse', 'Shipping', 'Delivery', 'Fulfillment']):
                    print(f"  • {line.strip()}")

    def display_invoice_summary(self, result: dict):
        """Display invoice generation summary"""
        print(f"\n{self.GREEN}{self.BOLD}Invoice Generation Summary:{self.END}")
        print(f"{self.GREEN}{'─'*40}{self.END}")

        status = result.get('status', 'UNKNOWN')
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}{status}{self.END}")

        # Extract detailed invoice information from result
        agent_result = result.get('agent_result', '') or result.get('invoice_generation', '')
        if agent_result:
            print(f"\n{self.CYAN}Invoice Generation Details:{self.END}")
            self._extract_invoice_details(agent_result)

        # Show execution time
        exec_time = result.get('execution_time', 0)
        print(f"{self.CYAN}Execution Time:{self.END} {exec_time:.2f} seconds")

    def _extract_invoice_details(self, agent_result: str):
        """Extract and display detailed invoice generation information"""
        try:
            lines = str(agent_result).split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for invoice-specific information
                if any(keyword in line for keyword in [
                    'Invoice Number:', 'Customer:', 'Total:', 'Email:', 'PDF:',
                    'Generated:', 'Sent:', 'Payment Terms:', 'Due Date:', 'Amount:',
                    'Invoice ID:', 'File:', 'Delivery Status:'
                ]):
                    if 'Generated' in line or 'Sent' in line or 'Created' in line:
                        print(f"  📧 {line}")
                    elif '$' in line or 'Total' in line or 'Amount' in line:
                        print(f"  💰 {line}")
                    else:
                        print(f"  • {line}")

        except Exception as e:
            # Fallback to simple extraction
            lines = str(agent_result).split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Invoice', 'Email', 'Generated', 'Total']):
                    print(f"  • {line.strip()}")

    def display_final_success(self, workflow_data: dict):
        """Display final success message"""
        self.print_header("WORKFLOW COMPLETED SUCCESSFULLY!")

        print(f"{self.GREEN}{self.BOLD}All 7 stages completed successfully!{self.END}")
        print(f"{self.GREEN}Complete Order-to-Cash workflow executed from PO to Invoice.{self.END}")

        # Show key metrics
        parsed_data = workflow_data.get('parsed_data', {})
        po_number = parsed_data.get('po_number', 'N/A')
        customer = parsed_data.get('customer', {}).get('name', 'N/A')
        total = parsed_data.get('pricing', {}).get('total', 0)

        print(f"\n{self.CYAN}{self.BOLD}Order Summary:{self.END}")
        print(f"{self.CYAN}PO Number:{self.END} {po_number}")
        print(f"{self.CYAN}Customer:{self.END} {customer}")
        print(f"{self.CYAN}Total Value:{self.END} ${total:,.2f}")
        print(f"{self.CYAN}Status:{self.END} {self.GREEN}INVOICE SENT{self.END}")

        print(f"\n{self.BLUE}{self.BOLD}Next Steps:{self.END}")
        print("• Monitor delivery tracking")
        print("• Follow up on payment")
        print("• Archive completed order")

    def run_complete_workflow(self):
        """Run the complete O2C workflow with user interaction"""
        self.print_header("O2C INTERACTIVE WORKFLOW")

        print("Welcome to the Simple O2C Interactive CLI!")
        print("This will guide you through the complete Order-to-Cash workflow.")
        print("You will be asked to approve each stage before execution.")

        if not self.get_user_approval("Start the O2C workflow?"):
            return

        # Get PO file
        po_filename = self.run_email_monitoring()
        if not po_filename:
            self.print_error("No PO file selected. Exiting.")
            return

        # Run stage-by-stage workflow
        self.run_stage_by_stage_workflow(po_filename)


def main():
    """Main CLI entry point"""
    cli = SimpleO2CCLI()
    
    try:
        cli.run_complete_workflow()
    except KeyboardInterrupt:
        print(f"\n\n{cli.RED}Workflow interrupted by user (Ctrl+C).{cli.END}")
    except Exception as e:
        print(f"\n\n{cli.RED}Unexpected error: {str(e)}{cli.END}")


if __name__ == "__main__":
    main()
