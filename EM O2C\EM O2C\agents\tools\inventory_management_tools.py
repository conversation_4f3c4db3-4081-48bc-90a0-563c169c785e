#!/usr/bin/env python3
"""
Inventory Management Tools for O2C System
Specialized tools for the Advanced Inventory Management Agent
"""

import os
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from crewai.tools import tool
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '../../database/o2c_master_data.db')


def get_db_connection():
    """Get database connection with proper error handling"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        return None


@tool("inventory_checker")
def check_inventory_levels(product_codes: str, warehouse_location: str = "MAIN") -> str:
    """
    Check current inventory levels for specified products.
    
    Args:
        product_codes: Comma-separated list of product codes to check
        warehouse_location: Warehouse location (default: MAIN)
        
    Returns:
        JSON string with inventory levels for each product
    """
    try:
        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})
        
        product_list = [code.strip() for code in product_codes.split(',')]
        inventory_data = {}
        
        for product_code in product_list:
            cursor = conn.execute("""
                SELECT il.*, p.description, p.status as product_status
                FROM inventory_levels il
                JOIN products p ON il.product_code = p.product_code
                WHERE il.product_code = ? AND il.warehouse_location = ?
            """, (product_code, warehouse_location))
            
            row = cursor.fetchone()
            if row:
                inventory_data[product_code] = {
                    "product_code": row["product_code"],
                    "description": row["description"],
                    "warehouse_location": row["warehouse_location"],
                    "quantity_on_hand": row["quantity_on_hand"],
                    "quantity_available": row["quantity_available"],
                    "quantity_allocated": row["quantity_allocated"],
                    "quantity_on_order": row["quantity_on_order"],
                    "reorder_point": row["reorder_point"],
                    "safety_stock": row["safety_stock"],
                    "product_status": row["product_status"],
                    "last_movement_date": row["last_movement_date"]
                }
            else:
                inventory_data[product_code] = {
                    "product_code": product_code,
                    "error": "Product not found in inventory or does not exist"
                }
        
        conn.close()
        return json.dumps(inventory_data, indent=2)
        
    except Exception as e:
        logger.error(f"Error checking inventory: {e}")
        return json.dumps({"error": f"Inventory check failed: {str(e)}"})


@tool("atp_calculator")
def calculate_available_to_promise(order_data: str) -> str:
    """
    Calculate Available-to-Promise (ATP) for order line items.
    
    Args:
        order_data: JSON string containing order line items with product_code and quantity
        
    Returns:
        JSON string with ATP calculations for each line item
    """
    try:
        order = json.loads(order_data)
        line_items = order.get("line_items", [])
        
        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})
        
        atp_results = []
        
        for item in line_items:
            product_code = item.get("product_code")
            requested_qty = item.get("quantity", 0)
            
            # Get current inventory levels
            cursor = conn.execute("""
                SELECT quantity_on_hand, quantity_available, quantity_allocated, 
                       reorder_point, safety_stock
                FROM inventory_levels 
                WHERE product_code = ? AND warehouse_location = 'MAIN'
            """, (product_code,))
            
            inventory = cursor.fetchone()
            
            if inventory:
                available_qty = inventory["quantity_available"]
                on_hand = inventory["quantity_on_hand"]
                allocated = inventory["quantity_allocated"]
                safety_stock = inventory["safety_stock"]
                
                # Calculate ATP considering safety stock
                atp_quantity = max(0, available_qty - safety_stock)
                
                # Determine fulfillment status
                if requested_qty <= atp_quantity:
                    fulfillment_status = "FULL_FULFILLMENT"
                    allocated_qty = requested_qty
                    backorder_qty = 0
                elif requested_qty <= available_qty:
                    fulfillment_status = "PARTIAL_FULFILLMENT_SAFETY_STOCK"
                    allocated_qty = atp_quantity
                    backorder_qty = requested_qty - atp_quantity
                else:
                    fulfillment_status = "BACKORDER_REQUIRED"
                    allocated_qty = atp_quantity
                    backorder_qty = requested_qty - atp_quantity
                
                # Get supplier lead time for backorder estimation
                cursor = conn.execute("""
                    SELECT MIN(lead_time_days) as min_lead_time
                    FROM supplier_lead_times 
                    WHERE product_code = ? AND (end_date IS NULL OR end_date > date('now'))
                """, (product_code,))
                
                lead_time_row = cursor.fetchone()
                lead_time = lead_time_row["min_lead_time"] if lead_time_row and lead_time_row["min_lead_time"] else 14
                
                estimated_availability = (datetime.now() + timedelta(days=lead_time)).strftime('%Y-%m-%d')
                
                atp_results.append({
                    "line_number": item.get("line_number", len(atp_results) + 1),
                    "product_code": product_code,
                    "description": item.get("description", ""),
                    "requested_quantity": requested_qty,
                    "atp_quantity": atp_quantity,
                    "allocated_quantity": allocated_qty,
                    "backorder_quantity": backorder_qty,
                    "fulfillment_status": fulfillment_status,
                    "inventory_details": {
                        "on_hand": on_hand,
                        "available": available_qty,
                        "allocated": allocated,
                        "safety_stock": safety_stock
                    },
                    "estimated_availability_date": estimated_availability if backorder_qty > 0 else None,
                    "lead_time_days": lead_time
                })
            else:
                atp_results.append({
                    "line_number": item.get("line_number", len(atp_results) + 1),
                    "product_code": product_code,
                    "description": item.get("description", ""),
                    "requested_quantity": requested_qty,
                    "error": "Product not found in inventory",
                    "fulfillment_status": "PRODUCT_NOT_FOUND"
                })
        
        conn.close()
        
        # Calculate overall order fulfillment summary
        total_lines = len(atp_results)
        full_fulfillment_lines = len([r for r in atp_results if r.get("fulfillment_status") == "FULL_FULFILLMENT"])
        partial_lines = len([r for r in atp_results if "PARTIAL" in r.get("fulfillment_status", "")])
        backorder_lines = len([r for r in atp_results if r.get("fulfillment_status") == "BACKORDER_REQUIRED"])
        
        overall_status = "FULL_FULFILLMENT"
        if backorder_lines > 0 or partial_lines > 0:
            if full_fulfillment_lines == 0:
                overall_status = "FULL_BACKORDER"
            else:
                overall_status = "PARTIAL_FULFILLMENT"
        
        result = {
            "order_reference": order.get("po_number", "UNKNOWN"),
            "atp_calculation_date": datetime.now().isoformat(),
            "overall_fulfillment_status": overall_status,
            "summary": {
                "total_line_items": total_lines,
                "full_fulfillment_lines": full_fulfillment_lines,
                "partial_fulfillment_lines": partial_lines,
                "backorder_lines": backorder_lines
            },
            "line_items": atp_results
        }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error calculating ATP: {e}")
        return json.dumps({"error": f"ATP calculation failed: {str(e)}"})


@tool("stock_allocator")
def allocate_inventory_stock(allocation_data: str) -> str:
    """
    Allocate inventory stock for confirmed orders.
    
    Args:
        allocation_data: JSON string with order reference and line items to allocate
        
    Returns:
        JSON string with allocation results
    """
    try:
        allocation_request = json.loads(allocation_data)
        order_reference = allocation_request.get("order_reference")
        line_items = allocation_request.get("line_items", [])
        warehouse_location = allocation_request.get("warehouse_location", "MAIN")
        
        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})
        
        allocation_results = []
        successful_allocations = []
        
        try:
            # Start transaction
            conn.execute("BEGIN TRANSACTION")
            
            for item in line_items:
                product_code = item.get("product_code")
                allocated_qty = item.get("allocated_quantity", 0)
                
                if allocated_qty <= 0:
                    continue
                
                # Check current available quantity
                cursor = conn.execute("""
                    SELECT quantity_available FROM inventory_levels 
                    WHERE product_code = ? AND warehouse_location = ?
                """, (product_code, warehouse_location))
                
                inventory = cursor.fetchone()
                
                if inventory and inventory["quantity_available"] >= allocated_qty:
                    # Create allocation record
                    conn.execute("""
                        INSERT INTO stock_allocations 
                        (product_code, warehouse_location, order_reference, allocated_quantity, 
                         expiry_date, status, created_by)
                        VALUES (?, ?, ?, ?, ?, 'ACTIVE', 'INVENTORY_AGENT')
                    """, (product_code, warehouse_location, order_reference, allocated_qty,
                          (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')))
                    
                    allocation_id = conn.lastrowid
                    
                    # Record inventory transaction
                    conn.execute("""
                        INSERT INTO inventory_transactions 
                        (product_code, warehouse_location, transaction_type, quantity_change,
                         reference_number, order_reference, reason_code, created_by)
                        VALUES (?, ?, 'ALLOCATION', ?, ?, ?, 'ORDER_ALLOCATION', 'INVENTORY_AGENT')
                    """, (product_code, warehouse_location, -allocated_qty, 
                          f"ALLOC-{allocation_id}", order_reference))
                    
                    successful_allocations.append({
                        "allocation_id": allocation_id,
                        "product_code": product_code,
                        "allocated_quantity": allocated_qty
                    })
                    
                    allocation_results.append({
                        "line_number": item.get("line_number"),
                        "product_code": product_code,
                        "requested_quantity": allocated_qty,
                        "allocated_quantity": allocated_qty,
                        "allocation_id": allocation_id,
                        "status": "SUCCESS"
                    })
                else:
                    available = inventory["quantity_available"] if inventory else 0
                    allocation_results.append({
                        "line_number": item.get("line_number"),
                        "product_code": product_code,
                        "requested_quantity": allocated_qty,
                        "allocated_quantity": 0,
                        "available_quantity": available,
                        "status": "INSUFFICIENT_INVENTORY",
                        "error": f"Requested {allocated_qty}, but only {available} available"
                    })
            
            # Commit transaction
            conn.execute("COMMIT")
            
            result = {
                "order_reference": order_reference,
                "allocation_date": datetime.now().isoformat(),
                "warehouse_location": warehouse_location,
                "total_allocations": len(successful_allocations),
                "successful_allocations": successful_allocations,
                "allocation_details": allocation_results,
                "status": "SUCCESS" if successful_allocations else "FAILED"
            }
            
            conn.close()
            return json.dumps(result, indent=2)
            
        except Exception as e:
            conn.execute("ROLLBACK")
            raise e
            
    except Exception as e:
        logger.error(f"Error allocating stock: {e}")
        return json.dumps({"error": f"Stock allocation failed: {str(e)}"})


@tool("backorder_scheduler")
def schedule_backorders(backorder_data: str) -> str:
    """
    Schedule backorders for items that cannot be fulfilled immediately.

    Args:
        backorder_data: JSON string with order reference and backorder line items

    Returns:
        JSON string with backorder scheduling results
    """
    try:
        backorder_request = json.loads(backorder_data)
        order_reference = backorder_request.get("order_reference")
        customer_id = backorder_request.get("customer_id")
        line_items = backorder_request.get("line_items", [])
        priority_level = backorder_request.get("priority_level", "MEDIUM")

        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})

        backorder_results = []

        for item in line_items:
            product_code = item.get("product_code")
            requested_qty = item.get("requested_quantity", 0)
            backorder_qty = item.get("backorder_quantity", 0)

            if backorder_qty <= 0:
                continue

            # Get supplier lead time for expected availability
            cursor = conn.execute("""
                SELECT MIN(lead_time_days) as min_lead_time, supplier_name
                FROM supplier_lead_times
                WHERE product_code = ? AND is_preferred = 1
                AND (end_date IS NULL OR end_date > date('now'))
            """, (product_code,))

            supplier_info = cursor.fetchone()
            lead_time = supplier_info["min_lead_time"] if supplier_info and supplier_info["min_lead_time"] else 14
            expected_date = (datetime.now() + timedelta(days=lead_time)).strftime('%Y-%m-%d')

            # Create backorder record
            cursor = conn.execute("""
                INSERT INTO backorders
                (order_reference, product_code, customer_id, requested_quantity,
                 backordered_quantity, priority_level, expected_availability_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'PENDING')
            """, (order_reference, product_code, customer_id, requested_qty,
                  backorder_qty, priority_level, expected_date))

            backorder_id = cursor.lastrowid

            backorder_results.append({
                "backorder_id": backorder_id,
                "line_number": item.get("line_number"),
                "product_code": product_code,
                "description": item.get("description", ""),
                "requested_quantity": requested_qty,
                "backordered_quantity": backorder_qty,
                "priority_level": priority_level,
                "expected_availability_date": expected_date,
                "lead_time_days": lead_time,
                "supplier": supplier_info["supplier_name"] if supplier_info else "TBD",
                "status": "SCHEDULED"
            })

        conn.commit()
        conn.close()

        result = {
            "order_reference": order_reference,
            "customer_id": customer_id,
            "backorder_date": datetime.now().isoformat(),
            "total_backorders": len(backorder_results),
            "backorder_details": backorder_results,
            "status": "SUCCESS"
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        logger.error(f"Error scheduling backorders: {e}")
        return json.dumps({"error": f"Backorder scheduling failed: {str(e)}"})


@tool("alternative_product_suggester")
def suggest_alternative_products(product_request: str) -> str:
    """
    Suggest alternative products when requested items are not available.

    Args:
        product_request: JSON string with product codes that need alternatives

    Returns:
        JSON string with alternative product suggestions
    """
    try:
        request = json.loads(product_request)
        product_codes = request.get("product_codes", [])

        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})

        suggestions = []

        for product_code in product_codes:
            # Get product details
            cursor = conn.execute("""
                SELECT p.*, il.quantity_available
                FROM products p
                LEFT JOIN inventory_levels il ON p.product_code = il.product_code
                WHERE p.product_code = ?
            """, (product_code,))

            original_product = cursor.fetchone()

            if original_product:
                category = original_product["category"]

                # Find alternative products in same category with available inventory
                cursor = conn.execute("""
                    SELECT p.product_code, p.description, p.unit_price, p.category,
                           il.quantity_available, p.status
                    FROM products p
                    JOIN inventory_levels il ON p.product_code = il.product_code
                    WHERE p.category = ? AND p.product_code != ?
                    AND p.status = 'ACTIVE' AND il.quantity_available > 0
                    ORDER BY il.quantity_available DESC, p.unit_price ASC
                    LIMIT 3
                """, (category, product_code))

                alternatives = []
                for alt in cursor.fetchall():
                    alternatives.append({
                        "product_code": alt["product_code"],
                        "description": alt["description"],
                        "unit_price": alt["unit_price"],
                        "category": alt["category"],
                        "available_quantity": alt["quantity_available"],
                        "price_difference": alt["unit_price"] - original_product["unit_price"],
                        "price_difference_percent": round(((alt["unit_price"] - original_product["unit_price"]) / original_product["unit_price"]) * 100, 2) if original_product["unit_price"] > 0 else 0
                    })

                suggestions.append({
                    "original_product_code": product_code,
                    "original_description": original_product["description"],
                    "original_price": original_product["unit_price"],
                    "original_available": original_product["quantity_available"] or 0,
                    "category": category,
                    "alternatives": alternatives,
                    "alternatives_found": len(alternatives)
                })
            else:
                suggestions.append({
                    "original_product_code": product_code,
                    "error": "Product not found",
                    "alternatives": [],
                    "alternatives_found": 0
                })

        conn.close()

        result = {
            "suggestion_date": datetime.now().isoformat(),
            "total_products_requested": len(product_codes),
            "suggestions": suggestions,
            "status": "SUCCESS"
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        logger.error(f"Error suggesting alternatives: {e}")
        return json.dumps({"error": f"Alternative suggestion failed: {str(e)}"})


@tool("inventory_updater")
def update_inventory_levels(update_data: str) -> str:
    """
    Update inventory levels for products (receipts, adjustments, etc.).

    Args:
        update_data: JSON string with inventory updates

    Returns:
        JSON string with update results
    """
    try:
        updates = json.loads(update_data)
        warehouse_location = updates.get("warehouse_location", "MAIN")
        transaction_type = updates.get("transaction_type", "ADJUSTMENT")
        reference_number = updates.get("reference_number", f"ADJ-{datetime.now().strftime('%Y%m%d%H%M%S')}")
        reason_code = updates.get("reason_code", "MANUAL_ADJUSTMENT")
        line_items = updates.get("line_items", [])

        conn = get_db_connection()
        if not conn:
            return json.dumps({"error": "Database connection failed"})

        update_results = []

        try:
            conn.execute("BEGIN TRANSACTION")

            for item in line_items:
                product_code = item.get("product_code")
                quantity_change = item.get("quantity_change", 0)
                notes = item.get("notes", "")

                # Check if inventory record exists
                cursor = conn.execute("""
                    SELECT inventory_id, quantity_on_hand, quantity_allocated
                    FROM inventory_levels
                    WHERE product_code = ? AND warehouse_location = ?
                """, (product_code, warehouse_location))

                inventory = cursor.fetchone()

                if inventory:
                    # Update existing inventory
                    new_on_hand = inventory["quantity_on_hand"] + quantity_change
                    new_available = new_on_hand - inventory["quantity_allocated"]

                    conn.execute("""
                        UPDATE inventory_levels
                        SET quantity_on_hand = ?, quantity_available = ?,
                            last_movement_date = datetime('now')
                        WHERE inventory_id = ?
                    """, (new_on_hand, new_available, inventory["inventory_id"]))

                    update_results.append({
                        "product_code": product_code,
                        "previous_on_hand": inventory["quantity_on_hand"],
                        "quantity_change": quantity_change,
                        "new_on_hand": new_on_hand,
                        "new_available": new_available,
                        "status": "UPDATED"
                    })
                else:
                    # Create new inventory record
                    if quantity_change > 0:  # Only create if positive quantity
                        conn.execute("""
                            INSERT INTO inventory_levels
                            (product_code, warehouse_location, quantity_on_hand,
                             quantity_available, last_movement_date)
                            VALUES (?, ?, ?, ?, datetime('now'))
                        """, (product_code, warehouse_location, quantity_change, quantity_change))

                        update_results.append({
                            "product_code": product_code,
                            "previous_on_hand": 0,
                            "quantity_change": quantity_change,
                            "new_on_hand": quantity_change,
                            "new_available": quantity_change,
                            "status": "CREATED"
                        })
                    else:
                        update_results.append({
                            "product_code": product_code,
                            "error": "Cannot create inventory with negative or zero quantity",
                            "status": "FAILED"
                        })
                        continue

                # Record transaction
                conn.execute("""
                    INSERT INTO inventory_transactions
                    (product_code, warehouse_location, transaction_type, quantity_change,
                     reference_number, reason_code, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'INVENTORY_AGENT')
                """, (product_code, warehouse_location, transaction_type, quantity_change,
                      reference_number, reason_code, notes))

            conn.execute("COMMIT")

            result = {
                "update_date": datetime.now().isoformat(),
                "warehouse_location": warehouse_location,
                "transaction_type": transaction_type,
                "reference_number": reference_number,
                "total_updates": len(update_results),
                "successful_updates": len([r for r in update_results if r.get("status") in ["UPDATED", "CREATED"]]),
                "update_details": update_results,
                "status": "SUCCESS"
            }

            conn.close()
            return json.dumps(result, indent=2)

        except Exception as e:
            conn.execute("ROLLBACK")
            raise e

    except Exception as e:
        logger.error(f"Error updating inventory: {e}")
        return json.dumps({"error": f"Inventory update failed: {str(e)}"})


def get_inventory_management_tools():
    """Return all inventory management tools for the agent"""
    return [
        check_inventory_levels,
        calculate_available_to_promise,
        allocate_inventory_stock,
        schedule_backorders,
        suggest_alternative_products,
        update_inventory_levels
    ]
