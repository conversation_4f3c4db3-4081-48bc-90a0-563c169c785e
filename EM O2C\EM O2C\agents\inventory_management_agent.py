#!/usr/bin/env python3
"""
Advanced Inventory Management Agent for O2C System
Handles real-time inventory checking, ATP calculations, stock allocation, and backorder management
"""

import os
import json
import sys
from datetime import datetime
from typing import Dict, Any, List
from crewai import Agent, Task, Crew, Process

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.llm.deepseek_llm import create_deepseek_llm_for_inventory
from agents.tools.inventory_management_tools import get_inventory_management_tools
from manage_verbosity import get_agent_config, get_crew_verbose
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class InventoryManagementAgent:
    """Advanced Inventory Management Agent using CrewAI and DeepSeek LLM"""
    
    def __init__(self, deepseek_api_key: str = None):
        """Initialize the Inventory Management Agent"""
        self.deepseek_api_key = deepseek_api_key or os.getenv("DEEPSEEK_API_KEY")
        
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key is required. Set DEEPSEEK_API_KEY environment variable.")
        
        # Set API key in environment for CrewAI
        os.environ["DEEPSEEK_API_KEY"] = self.deepseek_api_key
        
        self.llm = create_deepseek_llm_for_inventory()
        self.tools = get_inventory_management_tools()
        
        # Initialize agent
        self.inventory_agent = self._create_inventory_agent()
        
        # Create crew
        self.crew = self._create_crew()
    
    def _create_inventory_agent(self) -> Agent:
        """Create the Advanced Inventory Management Agent"""
        # Get verbosity setting from configuration
        agent_config = get_agent_config("inventory_management_agent")
        verbose = agent_config.get("verbose", False)
        allow_delegation = agent_config.get("allow_delegation", False)

        return Agent(
            role="Senior Inventory Management Specialist",
            goal="""Optimize inventory allocation and availability for order fulfillment while minimizing
            stockouts and excess inventory. Ensure accurate ATP calculations and efficient stock allocation.""",
            backstory="""You are a highly experienced inventory management specialist with 15+ years of
            experience in supply chain optimization, demand planning, and inventory control. You have deep
            expertise in:

            - Available-to-Promise (ATP) calculations and logic
            - Multi-location inventory management and allocation
            - Backorder management and customer communication
            - Safety stock optimization and reorder point calculations
            - Alternative product suggestions and substitution strategies
            - Real-time inventory tracking and transaction management

            You understand the critical balance between customer service levels and inventory carrying costs.
            You are meticulous in your calculations and always consider safety stock requirements, lead times,
            and customer priorities when making allocation decisions.

            Your decisions directly impact customer satisfaction, cash flow, and operational efficiency. You
            work closely with procurement, sales, and fulfillment teams to ensure optimal inventory performance.""",
            verbose=verbose,
            allow_delegation=allow_delegation,
            llm=self.llm,
            tools=self.tools,
            max_iter=5,
            memory=False  # Disabled to avoid OpenAI API dependency
        )
    
    def _create_crew(self) -> Crew:
        """Create the CrewAI crew"""
        crew_verbose = get_crew_verbose()
        return Crew(
            agents=[self.inventory_agent],
            tasks=[],  # Tasks will be added dynamically
            process=Process.sequential,
            verbose=crew_verbose,
            memory=False  # Disabled to avoid OpenAI API dependency
        )
    
    def create_inventory_assessment_task(self, parsed_order_data: Dict[str, Any], 
                                       validation_result: Dict[str, Any] = None,
                                       credit_result: Dict[str, Any] = None) -> Task:
        """Create a comprehensive inventory assessment task"""
        
        # Extract order information
        po_number = parsed_order_data.get("po_number", "UNKNOWN")
        customer_id = parsed_order_data.get("customer_info", {}).get("customer_id", "UNKNOWN")
        line_items = parsed_order_data.get("line_items", [])
        
        # Determine priority level based on customer and order characteristics
        priority_level = "MEDIUM"
        if credit_result and credit_result.get("credit_decision", {}).get("customer_tier") == "PREMIUM":
            priority_level = "HIGH"
        elif validation_result and validation_result.get("validation_summary", {}).get("risk_level") == "HIGH":
            priority_level = "LOW"
        
        task_description = f"""
        **INVENTORY MANAGEMENT TASK: {po_number}**
        
        You are tasked with performing comprehensive inventory management for a credit-approved order. 
        Your goal is to determine inventory availability, calculate ATP, allocate stock, and handle any 
        backorder requirements.
        
        **ORDER DETAILS:**
        - PO Number: {po_number}
        - Customer ID: {customer_id}
        - Total Line Items: {len(line_items)}
        - Priority Level: {priority_level}
        
        **VALIDATION CONTEXT:**
        {json.dumps(validation_result, indent=2) if validation_result else "No validation data provided"}
        
        **CREDIT CONTEXT:**
        {json.dumps(credit_result, indent=2) if credit_result else "No credit data provided"}
        
        **PARSED ORDER DATA:**
        {json.dumps(parsed_order_data, indent=2)}
        
        **YOUR TASKS:**
        
        1. **INVENTORY AVAILABILITY CHECK**
           - Use the inventory_checker tool to check current inventory levels for all products
           - Analyze quantity on hand, available, allocated, and safety stock levels
           - Identify any products with insufficient inventory
        
        2. **ATP CALCULATION**
           - Use the atp_calculator tool to perform detailed Available-to-Promise calculations
           - Consider safety stock requirements and existing allocations
           - Determine fulfillment status for each line item (full, partial, backorder)
           - Calculate expected availability dates for backorder items
        
        3. **STOCK ALLOCATION**
           - For items that can be fulfilled, use the stock_allocator tool to reserve inventory
           - Ensure allocations are properly recorded with expiry dates
           - Handle allocation failures gracefully
        
        4. **BACKORDER MANAGEMENT**
           - For items requiring backorders, use the backorder_scheduler tool
           - Set appropriate priority levels based on customer tier and urgency
           - Calculate realistic expected availability dates based on supplier lead times
        
        5. **ALTERNATIVE PRODUCT SUGGESTIONS**
           - For unavailable items, use the alternative_product_suggester tool
           - Identify suitable substitutes in the same category
           - Consider price differences and availability
        
        6. **INVENTORY IMPACT ANALYSIS**
           - Analyze the impact of this order on overall inventory levels
           - Identify products approaching reorder points
           - Recommend any inventory adjustments if needed
        
        **DELIVERABLES:**
        
        Provide a comprehensive inventory management report including:
        - Detailed ATP analysis for each line item
        - Stock allocation confirmations
        - Backorder schedules with expected dates
        - Alternative product recommendations
        - Inventory impact assessment
        - Recommendations for next steps (proceed to pricing, customer notification, etc.)
        
        **QUALITY STANDARDS:**
        - All calculations must be accurate and consider safety stock
        - Allocations must be properly recorded in the database
        - Backorder dates must be realistic based on supplier lead times
        - Alternative suggestions must be relevant and available
        - Clear communication of any issues or constraints
        
        Use your expertise to optimize inventory allocation while maintaining high customer service levels.
        """
        
        return Task(
            description=task_description,
            agent=self.inventory_agent,
            expected_output="""A comprehensive inventory management report in JSON format containing:
            {
                "inventory_assessment": {
                    "po_number": "string",
                    "assessment_date": "ISO datetime",
                    "overall_fulfillment_status": "FULL_FULFILLMENT|PARTIAL_FULFILLMENT|BACKORDER_REQUIRED",
                    "inventory_check_results": {...},
                    "atp_calculations": {...},
                    "stock_allocations": {...},
                    "backorder_schedule": {...},
                    "alternative_suggestions": {...},
                    "inventory_impact": {...},
                    "recommendations": [...],
                    "next_steps": "string"
                }
            }"""
        )
    
    def assess_inventory_for_order(self, parsed_order_data: Dict[str, Any], 
                                 validation_result: Dict[str, Any] = None,
                                 credit_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform comprehensive inventory assessment for an order
        
        Args:
            parsed_order_data: Parsed order data from PO Parser Agent
            validation_result: Results from Order Validation Agent
            credit_result: Results from Credit Management Agent
            
        Returns:
            Inventory assessment results
        """
        try:
            logger.info(f"🏭 Starting inventory assessment for PO: {parsed_order_data.get('po_number', 'UNKNOWN')}")
            
            # Create and execute inventory assessment task
            task = self.create_inventory_assessment_task(parsed_order_data, validation_result, credit_result)
            self.crew.tasks = [task]
            
            # Execute the crew
            result = self.crew.kickoff()

            # Parse the result - handle CrewOutput object
            if hasattr(result, 'raw'):
                # CrewAI returns CrewOutput object with .raw attribute
                result_text = str(result.raw)
            else:
                # Fallback for other result types
                result_text = str(result)

            # Try to parse as JSON, otherwise use as raw text
            try:
                inventory_result = json.loads(result_text)
            except json.JSONDecodeError:
                inventory_result = {"raw_output": result_text}

            # Ensure inventory_result is a dictionary before updating
            if not isinstance(inventory_result, dict):
                inventory_result = {"raw_output": str(inventory_result)}

            # Add metadata
            inventory_result.update({
                "agent_type": "inventory_management",
                "processing_timestamp": datetime.now().isoformat(),
                "status": "SUCCESS"
            })
            
            logger.info(" Inventory assessment completed successfully")
            return inventory_result
            
        except Exception as e:
            logger.error(f" Inventory assessment failed: {str(e)}")
            return {
                "agent_type": "inventory_management",
                "processing_timestamp": datetime.now().isoformat(),
                "status": "ERROR",
                "error": str(e),
                "inventory_assessment": {
                    "po_number": parsed_order_data.get("po_number", "UNKNOWN"),
                    "overall_fulfillment_status": "ASSESSMENT_FAILED",
                    "error_details": str(e)
                }
            }


def run_inventory_assessment_demo(parsed_file_path: str = None):
    """Demo function to test inventory assessment"""
    try:
        # Load parsed order data
        if not parsed_file_path:
            # Find the most recent parsed file
            parsed_dir = "data/parsed_pos"
            if os.path.exists(parsed_dir):
                parsed_files = [f for f in os.listdir(parsed_dir) if f.startswith("PARSED_") and f.endswith(".json")]
                if parsed_files:
                    parsed_files.sort(reverse=True)
                    parsed_file_path = os.path.join(parsed_dir, parsed_files[0])
        
        if not parsed_file_path or not os.path.exists(parsed_file_path):
            print(" No parsed order file found. Please run PO parsing first.")
            return
        
        print(f"📄 Loading parsed order from: {parsed_file_path}")
        with open(parsed_file_path, 'r') as f:
            parsed_order = json.load(f)
        
        # Create inventory agent
        print("🏭 Initializing Inventory Management Agent...")
        agent = InventoryManagementAgent()
        
        # Run inventory assessment
        print(" Running inventory assessment...")
        result = agent.assess_inventory_for_order(parsed_order)
        
        # Display results
        print("\n" + "="*60)
        print("🏭 INVENTORY ASSESSMENT RESULTS")
        print("="*60)
        print(json.dumps(result, indent=2))
        
        return result
        
    except Exception as e:
        print(f" Demo failed: {str(e)}")
        return None


if __name__ == "__main__":
    run_inventory_assessment_demo()
