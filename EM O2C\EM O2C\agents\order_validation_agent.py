#!/usr/bin/env python3
"""
Comprehensive Order Validation Agent
Multi-layer validation with business rules and compliance checks
"""

import os
import sys
import json
from crewai import Agent, Task, Crew
from agents.llm.deepseek_llm import create_deepseek_llm_for_parsing
from agents.tools.order_validation_tools import get_order_validation_tools
from dotenv import load_dotenv
from manage_verbosity import get_agent_config, get_crew_verbose

# Load environment variables
load_dotenv()


def create_order_validation_agent():
    """Create the Comprehensive Order Validation Agent"""
    
    # Get DeepSeek LLM
    llm = create_deepseek_llm_for_parsing()
    
    # Get validation tools
    tools = get_order_validation_tools()
    
    # Get verbosity setting from configuration
    agent_config = get_agent_config("order_validation_agent")
    verbose = agent_config.get("verbose", False)
    allow_delegation = agent_config.get("allow_delegation", False)

    # Create the agent
    agent = Agent(
        role="Senior Order Validation Specialist",
        goal="Perform comprehensive multi-layer validation of parsed order data with business rules and compliance checks to ensure order accuracy, customer eligibility, and regulatory compliance",
        backstory="""You are a highly experienced Order Validation Specialist with 15+ years in enterprise
        order management and compliance. You have deep expertise in:

        - Customer credit and risk assessment
        - Product catalog management and pricing validation
        - Business rules enforcement and exception handling
        - Regulatory compliance (SOX, tax, export controls)
        - Delivery logistics and address verification
        - Risk scoring and approval workflows

        You are meticulous, thorough, and always ensure that orders meet all business and regulatory
        requirements before approval. You provide clear, actionable recommendations for any issues found.""",
        tools=tools,
        llm=llm,
        memory=False,  # Disabled to avoid OpenAI API dependency
        verbose=verbose,
        allow_delegation=allow_delegation
    )
    
    return agent


def create_order_validation_task(parsed_order_file: str = ""):
    """Create comprehensive order validation task"""
    
    if not parsed_order_file:
        # Find the most recent parsed order file
        parsed_dir = "data/parsed_pos"
        if os.path.exists(parsed_dir):
            files = [f for f in os.listdir(parsed_dir) if f.startswith("PARSED_") and f.endswith(".json")]
            if files:
                files.sort(reverse=True)  # Most recent first
                parsed_order_file = files[0]
            else:
                parsed_order_file = "No parsed order files found"
        else:
            parsed_order_file = "Parsed orders directory not found"
    
    task = Task(
        description=f"""
        Perform comprehensive validation of the parsed order data from file: {parsed_order_file}
        
        VALIDATION WORKFLOW:
        
        STEP 1: Load and analyze the parsed order data
        - Use parsed_order_reader tool to read the file: {parsed_order_file}
        - Extract key order information (customer, products, pricing, delivery)
        - Verify data completeness and structure
        
        STEP 2: Customer validation and credit assessment
        - Use customer_validator tool to verify customer existence and status
        - Check customer credit limits against order total
        - Assess customer risk profile and payment history
        - Validate customer information completeness
        
        STEP 3: Product catalog and pricing validation
        - Use product_catalog_validator tool to verify all line items
        - Check product codes against catalog
        - Validate pricing against catalog prices
        - Verify product availability and status
        - Check minimum order quantities and restrictions
        
        STEP 4: Business rules validation
        - Use business_rules_validator tool to apply all business rules
        - Check order value limits (minimum/maximum)
        - Validate required fields and data formats
        - Verify line item counts and restrictions
        - Check delivery lead time requirements
        
        STEP 5: Delivery address and logistics validation
        - Use delivery_address_validator tool to verify delivery information
        - Check delivery addresses against customer approved locations
        - Validate delivery dates and logistics requirements
        - Identify any shipping restrictions or special requirements
        
        STEP 6: Regulatory compliance validation
        - Use compliance_validator tool to perform compliance checks
        - Verify tax calculations and exemptions
        - Check export control requirements
        - Ensure audit trail completeness
        - Validate data privacy and security requirements
        
        STEP 7: Risk assessment and final recommendation
        - Use risk_assessment_calculator tool to calculate overall risk score
        - Compile all validation results into comprehensive report
        - Determine final validation status (PASS/REVIEW/FAIL)
        - Provide clear recommendations for next steps
        
        EXPECTED OUTPUT:
        Generate a comprehensive validation report with:
        - Overall validation status (PASS/REVIEW/FAIL)
        - Detailed results from each validation layer
        - Risk assessment score and level
        - Clear recommendations for order processing
        - Any required approvals or manual reviews
        - Complete audit trail of validation performed
        
        VALIDATION CRITERIA:
        - PASS: All validations successful, low risk, ready for processing
        - REVIEW: Some warnings or medium risk, requires manual review
        - FAIL: Critical issues found, order cannot be processed as-is
        
        Be thorough, accurate, and provide actionable insights for order processing decisions.
        """,
        expected_output="""A comprehensive order validation report containing:
        1. Executive summary with overall validation status
        2. Detailed validation results for each layer (customer, product, business rules, delivery, compliance)
        3. Risk assessment with score and level
        4. Clear recommendations and next steps
        5. Complete audit trail of validation performed
        6. Any required approvals or escalations""",
        agent=None  # Will be set when creating the crew
    )
    
    return task


def run_order_validation(parsed_order_file: str = ""):
    """Run the comprehensive order validation workflow"""
    
    print(" Starting Comprehensive Order Validation...")
    print("=" * 60)
    
    # Create agent and task
    agent = create_order_validation_agent()
    task = create_order_validation_task(parsed_order_file)
    
    # Assign agent to task
    task.agent = agent

    # Create crew
    crew_verbose = get_crew_verbose()
    crew = Crew(
        agents=[agent],
        tasks=[task],
        verbose=crew_verbose,
        process="sequential"
    )
    
    # Execute validation
    try:
        result = crew.kickoff()
        
        print("\n" + "=" * 60)
        print(" Order Validation Complete!")
        print("=" * 60)
        
        return result
        
    except Exception as e:
        print(f"\n Error during order validation: {str(e)}")
        return None


if __name__ == "__main__":
    # Check for command line arguments
    parsed_file = ""
    if len(sys.argv) > 1:
        parsed_file = sys.argv[1]
    
    # Run validation
    result = run_order_validation(parsed_file)
    
    if result:
        print(f"\n📊 Validation Result:\n{result}")
    else:
        print("\n Validation failed to complete")
