#!/usr/bin/env python3
"""
O2C Interactive CLI Interface
Provides step-by-step control over the complete Order-to-Cash workflow
with user approval required for each stage.
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import O2C components
try:
    from services.email_monitor import EmailMonitor
    from services.workflow_orchestrator import O2CWorkflowOrchestrator
    from agents.po_parser_agent import POParserAgent
    from agents.order_validation_agent import OrderValidationAgent
    from agents.credit_management_agent import CreditManagementAgent
    from agents.inventory_management_agent import InventoryManagementAgent
    from agents.pricing_management_agent import PricingManagementAgent
    from agents.fulfillment_coordination_agent import FulfillmentCoordinationAgent
    from agents.invoice_generation_agent import InvoiceGenerationAgent
except ImportError as e:
    print(f"Error importing O2C components: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


class O2CCLI:
    """Interactive CLI for O2C Workflow Management"""

    def __init__(self):
        """Initialize CLI interface"""
        self.email_monitor = None
        self.workflow_orchestrator = O2CWorkflowOrchestrator()
        self.current_workflow_id = None
        self.workflow_data = {}
        self.stage_results = {}
        
        # CLI styling
        self.HEADER = '\033[95m'
        self.BLUE = '\033[94m'
        self.CYAN = '\033[96m'
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BOLD = '\033[1m'
        self.UNDERLINE = '\033[4m'
        self.END = '\033[0m'
        
    def print_header(self, text: str):
        """Print styled header"""
        print(f"\n{self.HEADER}{self.BOLD}{'='*80}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{text.center(80)}{self.END}")
        print(f"{self.HEADER}{self.BOLD}{'='*80}{self.END}\n")
        
    def print_stage(self, stage_num: int, stage_name: str):
        """Print stage header"""
        print(f"\n{self.CYAN}{self.BOLD}Stage {stage_num}: {stage_name}{self.END}")
        print(f"{self.CYAN}{'─'*60}{self.END}")
        
    def print_success(self, text: str):
        """Print success message"""
        print(f"{self.GREEN}✓ {text}{self.END}")
        
    def print_warning(self, text: str):
        """Print warning message"""
        print(f"{self.YELLOW}⚠ {text}{self.END}")
        
    def print_error(self, text: str):
        """Print error message"""
        print(f"{self.RED}✗ {text}{self.END}")
        
    def print_info(self, text: str):
        """Print info message"""
        print(f"{self.BLUE}ℹ {text}{self.END}")
        
    def get_user_approval(self, prompt: str = "Continue to next stage?") -> bool:
        """Get user approval to proceed"""
        while True:
            response = input(f"\n{self.YELLOW}► {prompt} (y/n/q): {self.END}").lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            elif response in ['q', 'quit']:
                print(f"\n{self.RED}Workflow terminated by user.{self.END}")
                sys.exit(0)
            else:
                print(f"{self.RED}Please enter 'y' for yes, 'n' for no, or 'q' to quit.{self.END}")
    
    def display_json_data(self, data: Dict, title: str = "Data"):
        """Display JSON data in a formatted way"""
        print(f"\n{self.BLUE}{self.BOLD}{title}:{self.END}")
        print(f"{self.BLUE}{'─'*40}{self.END}")
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    print(f"{self.CYAN}{key}:{self.END} [Complex object - {len(value)} items]")
                else:
                    print(f"{self.CYAN}{key}:{self.END} {value}")
        else:
            print(json.dumps(data, indent=2))
    
    def run_email_monitoring(self) -> Optional[str]:
        """Run email monitoring with user interaction"""
        self.print_stage(0, "Email Monitoring")
        
        print("Starting email monitoring for new purchase orders...")
        print("This will check your configured Gmail account for new PO emails.")
        
        if not self.get_user_approval("Start email monitoring?"):
            return None
            
        try:
            self.email_monitor = EmailMonitor()
            print(f"\n{self.BLUE}Checking for new emails...{self.END}")
            
            # Run single monitoring cycle
            result = self.email_monitor.run_monitoring_cycle()
            
            if result and result.get('new_pos_found', 0) > 0:
                po_files = result.get('po_files', [])
                self.print_success(f"Found {len(po_files)} new purchase order(s)")
                
                for po_file in po_files:
                    print(f"  • {po_file}")
                
                # Let user select which PO to process
                if len(po_files) == 1:
                    selected_po = po_files[0]
                    self.print_info(f"Processing: {selected_po}")
                else:
                    print(f"\n{self.YELLOW}Multiple POs found. Select one to process:{self.END}")
                    for i, po_file in enumerate(po_files, 1):
                        print(f"  {i}. {po_file}")
                    
                    while True:
                        try:
                            choice = int(input(f"\n{self.YELLOW}Enter choice (1-{len(po_files)}): {self.END}"))
                            if 1 <= choice <= len(po_files):
                                selected_po = po_files[choice - 1]
                                break
                            else:
                                print(f"{self.RED}Invalid choice. Please enter 1-{len(po_files)}.{self.END}")
                        except ValueError:
                            print(f"{self.RED}Please enter a valid number.{self.END}")
                
                return selected_po
                
            else:
                self.print_warning("No new purchase orders found")
                print("You can:")
                print("1. Wait and try again")
                print("2. Use a test PO file")
                print("3. Exit")
                
                choice = input(f"\n{self.YELLOW}Enter choice (1-3): {self.END}")
                if choice == "2":
                    return self.select_test_po()
                elif choice == "3":
                    return None
                else:
                    return self.run_email_monitoring()  # Try again
                    
        except Exception as e:
            self.print_error(f"Email monitoring failed: {str(e)}")
            return None
    
    def select_test_po(self) -> Optional[str]:
        """Let user select a test PO file"""
        incoming_dir = Path("data/incoming_pos")
        if not incoming_dir.exists():
            self.print_error("Incoming POs directory not found")
            return None
            
        po_files = list(incoming_dir.glob("*.txt"))
        if not po_files:
            self.print_warning("No PO files found in incoming directory")
            
            # Offer to create a test PO
            if self.get_user_approval("Create a test PO file?"):
                return self.create_test_po()
            return None
        
        print(f"\n{self.BLUE}Available PO files:{self.END}")
        for i, po_file in enumerate(po_files, 1):
            print(f"  {i}. {po_file.name}")
        
        while True:
            try:
                choice = int(input(f"\n{self.YELLOW}Select PO file (1-{len(po_files)}): {self.END}"))
                if 1 <= choice <= len(po_files):
                    return po_files[choice - 1].name
                else:
                    print(f"{self.RED}Invalid choice. Please enter 1-{len(po_files)}.{self.END}")
            except ValueError:
                print(f"{self.RED}Please enter a valid number.{self.END}")
    
    def create_test_po(self) -> str:
        """Create a test PO file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        po_number = f"PO-CLI-TEST-{timestamp}"
        filename = f"PO_{timestamp}.txt"
        
        test_po_content = f"""=== EMAIL METADATA ===
Email ID: CLI-TEST-{timestamp}
Timestamp: {datetime.now().isoformat()}
Sender: <EMAIL>
Subject: Purchase Order {po_number}
Date: {datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}

=== EMAIL BODY ===
Purchase Order: {po_number}
Customer: Test Customer Corp

Line Items:
- WIDGET-A100: 100 x $125.00
- BOLT-M8-50: 200 x $2.50

Total: $13000.00
Delivery Date: 2025-08-01
Payment Terms: Net 30

=== TEXT ATTACHMENTS ===
ATTACHMENT: purchase_order_details.txt

PURCHASE ORDER
==============
PO Number: {po_number}
Date: {datetime.now().strftime('%Y-%m-%d')}

VENDOR:
Your Company Name
123 Vendor Street
Business City, ST 12345

CUSTOMER:
Test Customer Corp
123 Business Street
Anytown, CA 90210

SHIP TO:
456 Industrial Blvd
Anytown, CA 90211

LINE ITEMS:
1. WIDGET-A100 - Premium Widget Assembly Type A
   Quantity: 100
   Unit Price: $125.00
   Line Total: $12500.00

2. BOLT-M8-50 - M8 x 50mm Stainless Steel Bolts
   Quantity: 200
   Unit Price: $2.50
   Line Total: $500.00

PRICING SUMMARY:
Subtotal: $13000.00
Tax (8.0%): $1040.00
Shipping: $100.00
TOTAL: $14140.00

DELIVERY:
Requested Date: 2025-08-01
Address: 456 Industrial Blvd, Anytown, CA 90211

TERMS:
Payment Terms: Net 30
Currency: USD

Authorized by: Test User
Contact: <EMAIL>
Phone: 555-0123
"""
        
        # Create directories if they don't exist
        os.makedirs("data/incoming_pos", exist_ok=True)
        
        # Write test PO file
        po_path = Path("data/incoming_pos") / filename
        with open(po_path, 'w') as f:
            f.write(test_po_content)
        
        self.print_success(f"Created test PO: {filename}")
        return filename

    def run_stage_1_parsing(self, po_filename: str) -> bool:
        """Run Stage 1: PO Parsing with user interaction"""
        self.print_stage(1, "Purchase Order Parsing")

        print(f"Processing PO file: {po_filename}")
        print("This stage will extract structured data from the purchase order.")

        if not self.get_user_approval("Run PO parsing?"):
            return False

        try:
            print(f"\n{self.BLUE}Parsing purchase order...{self.END}")

            # Use workflow orchestrator to run parsing
            po_file_path = f"data/incoming_pos/{po_filename}"
            result = self.workflow_orchestrator._run_po_parser(po_file_path)

            if result and result.get('status') == 'SUCCESS':
                self.print_success("PO parsing completed successfully")

                # Get parsed file name
                parsed_file = result.get('parsed_file', '')
                if parsed_file:
                    # Load parsed data to display summary
                    parsed_file_path = f"data/parsed_pos/{parsed_file}"
                    if os.path.exists(parsed_file_path):
                        with open(parsed_file_path, 'r') as f:
                            parsed_data = json.load(f)
                        self.display_parsing_summary(parsed_data)
                        self.workflow_data['parsed_data'] = parsed_data

                # Store result for next stage
                self.stage_results['parsing'] = result
                self.workflow_data['po_filename'] = po_filename
                self.workflow_data['parsed_file'] = parsed_file

                return True
            else:
                self.print_error(f"PO parsing failed: {result.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            self.print_error(f"PO parsing error: {str(e)}")
            return False

    def display_parsing_summary(self, parsed_data: Dict):
        """Display summary of parsed PO data"""
        print(f"\n{self.GREEN}{self.BOLD}Parsed PO Summary:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Basic info
        po_number = parsed_data.get('po_number', 'N/A')
        date = parsed_data.get('date', 'N/A')
        print(f"{self.CYAN}PO Number:{self.END} {po_number}")
        print(f"{self.CYAN}Date:{self.END} {date}")

        # Customer info
        customer = parsed_data.get('customer', {})
        customer_name = customer.get('name', 'N/A')
        print(f"{self.CYAN}Customer:{self.END} {customer_name}")

        # Line items
        line_items = parsed_data.get('line_items', [])
        print(f"{self.CYAN}Line Items:{self.END} {len(line_items)} items")
        for i, item in enumerate(line_items[:3], 1):  # Show first 3 items
            item_code = item.get('item_code', 'N/A')
            quantity = item.get('quantity', 0)
            unit_price = item.get('unit_price', 0)
            print(f"  {i}. {item_code}: {quantity} x ${unit_price:.2f}")

        if len(line_items) > 3:
            print(f"  ... and {len(line_items) - 3} more items")

        # Pricing
        pricing = parsed_data.get('pricing', {})
        total = pricing.get('total', 0)
        print(f"{self.CYAN}Total Amount:{self.END} ${total:.2f}")

        # Delivery
        delivery = parsed_data.get('delivery', {})
        delivery_date = delivery.get('requested_date', 'N/A')
        print(f"{self.CYAN}Delivery Date:{self.END} {delivery_date}")

    def run_stage_2_validation(self) -> bool:
        """Run Stage 2: Order Validation with user interaction"""
        self.print_stage(2, "Order Validation")

        print("This stage will perform comprehensive validation of the parsed order.")
        print("Validation includes customer verification, product catalog checks, and business rules.")

        if not self.get_user_approval("Run order validation?"):
            return False

        try:
            print(f"\n{self.BLUE}Running comprehensive order validation...{self.END}")

            # Get parsed file from previous stage
            parsed_file = self.workflow_data.get('parsed_file', '')

            # Run validation using workflow orchestrator
            result = self.workflow_orchestrator._run_order_validation(parsed_file)

            if result:
                self.display_validation_results(result)

                # Store result for next stage
                self.stage_results['validation'] = result

                # Check if validation passed
                validation_status = result.get('status', 'UNKNOWN')
                if validation_status in ['SUCCESS', 'REVIEW']:
                    self.print_success(f"Order validation completed: {validation_status}")
                    return True
                else:
                    self.print_error(f"Order validation failed: {validation_status}")
                    return False
            else:
                self.print_error("Order validation failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Order validation error: {str(e)}")
            return False

    def display_validation_results(self, result: Dict):
        """Display validation results summary"""
        print(f"\n{self.GREEN}{self.BOLD}Validation Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Overall status
        overall_status = result.get('overall_status', 'UNKNOWN')
        status_color = self.GREEN if overall_status == 'PASS' else self.YELLOW if overall_status == 'REVIEW' else self.RED
        print(f"{self.CYAN}Overall Status:{self.END} {status_color}{overall_status}{self.END}")

        # Risk assessment
        risk_score = result.get('risk_score', 0)
        risk_level = result.get('risk_level', 'UNKNOWN')
        print(f"{self.CYAN}Risk Score:{self.END} {risk_score} ({risk_level})")

        # Validation layers
        validations = result.get('validations', {})
        print(f"\n{self.CYAN}Validation Layers:{self.END}")

        for layer, status in validations.items():
            status_symbol = "✓" if status == "PASS" else "⚠" if status == "REVIEW" else "✗"
            status_color = self.GREEN if status == "PASS" else self.YELLOW if status == "REVIEW" else self.RED
            print(f"  {status_color}{status_symbol} {layer}: {status}{self.END}")

        # Recommendations
        recommendations = result.get('recommendations', [])
        if recommendations:
            print(f"\n{self.CYAN}Recommendations:{self.END}")
            for rec in recommendations[:3]:  # Show first 3 recommendations
                print(f"  • {rec}")

    def run_stage_3_credit(self) -> bool:
        """Run Stage 3: Credit Assessment with user interaction"""
        self.print_stage(3, "Credit Assessment")

        print("This stage will assess customer creditworthiness and payment risk.")
        print("Credit limits, payment history, and risk factors will be evaluated.")

        if not self.get_user_approval("Run credit assessment?"):
            return False

        try:
            # Initialize Credit Management Agent
            credit_agent = CreditManagementAgent()

            print(f"\n{self.BLUE}Assessing customer credit and risk...{self.END}")

            # Get customer and order data
            parsed_data = self.workflow_data.get('parsed_data', {})
            customer_name = parsed_data.get('customer', {}).get('name', '')
            order_total = parsed_data.get('pricing', {}).get('total', 0)

            # Run credit assessment
            result = credit_agent.assess_credit(customer_name, order_total)

            if result:
                self.display_credit_results(result)

                # Store result for next stage
                self.stage_results['credit'] = result

                # Check if credit assessment passed
                credit_decision = result.get('decision', 'UNKNOWN')
                if credit_decision in ['APPROVE', 'APPROVE_WITH_CONDITIONS']:
                    self.print_success(f"Credit assessment completed: {credit_decision}")
                    return True
                else:
                    self.print_error(f"Credit assessment failed: {credit_decision}")
                    return False
            else:
                self.print_error("Credit assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Credit assessment error: {str(e)}")
            return False

    def display_credit_results(self, result: Dict):
        """Display credit assessment results"""
        print(f"\n{self.GREEN}{self.BOLD}Credit Assessment Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Credit decision
        decision = result.get('decision', 'UNKNOWN')
        decision_color = self.GREEN if decision.startswith('APPROVE') else self.RED
        print(f"{self.CYAN}Credit Decision:{self.END} {decision_color}{decision}{self.END}")

        # Customer info
        customer_info = result.get('customer_info', {})
        credit_limit = customer_info.get('credit_limit', 0)
        available_credit = customer_info.get('available_credit', 0)
        credit_score = customer_info.get('credit_score', 0)

        print(f"{self.CYAN}Credit Limit:{self.END} ${credit_limit:,.2f}")
        print(f"{self.CYAN}Available Credit:{self.END} ${available_credit:,.2f}")
        print(f"{self.CYAN}Credit Score:{self.END} {credit_score}")

        # Risk assessment
        risk_level = result.get('risk_level', 'UNKNOWN')
        print(f"{self.CYAN}Risk Level:{self.END} {risk_level}")

        # Conditions (if any)
        conditions = result.get('conditions', [])
        if conditions:
            print(f"\n{self.CYAN}Approval Conditions:{self.END}")
            for condition in conditions:
                print(f"  • {condition}")

    def run_stage_4_inventory(self) -> bool:
        """Run Stage 4: Inventory Assessment with user interaction"""
        self.print_stage(4, "Inventory Assessment")

        print("This stage will check product availability and plan fulfillment.")
        print("Stock levels, lead times, and fulfillment options will be evaluated.")

        if not self.get_user_approval("Run inventory assessment?"):
            return False

        try:
            # Initialize Inventory Management Agent
            inventory_agent = InventoryManagementAgent()

            print(f"\n{self.BLUE}Checking inventory and planning fulfillment...{self.END}")

            # Get line items from parsed data
            parsed_data = self.workflow_data.get('parsed_data', {})
            line_items = parsed_data.get('line_items', [])

            # Run inventory assessment
            result = inventory_agent.assess_inventory(line_items)

            if result:
                self.display_inventory_results(result)

                # Store result for next stage
                self.stage_results['inventory'] = result

                # Check if inventory assessment passed
                fulfillment_status = result.get('fulfillment_status', 'UNKNOWN')
                if fulfillment_status in ['FULL_FULFILLMENT', 'PARTIAL_FULFILLMENT']:
                    self.print_success(f"Inventory assessment completed: {fulfillment_status}")
                    return True
                else:
                    self.print_error(f"Inventory assessment failed: {fulfillment_status}")
                    return False
            else:
                self.print_error("Inventory assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Inventory assessment error: {str(e)}")
            return False

    def display_inventory_results(self, result: Dict):
        """Display inventory assessment results"""
        print(f"\n{self.GREEN}{self.BOLD}Inventory Assessment Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Fulfillment status
        fulfillment_status = result.get('fulfillment_status', 'UNKNOWN')
        status_color = self.GREEN if fulfillment_status == 'FULL_FULFILLMENT' else self.YELLOW
        print(f"{self.CYAN}Fulfillment Status:{self.END} {status_color}{fulfillment_status}{self.END}")

        # Item availability
        item_results = result.get('item_results', [])
        print(f"\n{self.CYAN}Item Availability:{self.END}")

        for item in item_results[:5]:  # Show first 5 items
            item_code = item.get('item_code', 'N/A')
            status = item.get('status', 'UNKNOWN')
            available_qty = item.get('available_quantity', 0)
            requested_qty = item.get('requested_quantity', 0)

            status_symbol = "✓" if status == "AVAILABLE" else "⚠" if status == "PARTIAL" else "✗"
            status_color = self.GREEN if status == "AVAILABLE" else self.YELLOW if status == "PARTIAL" else self.RED

            print(f"  {status_color}{status_symbol} {item_code}: {available_qty}/{requested_qty} available{self.END}")

        if len(item_results) > 5:
            print(f"  ... and {len(item_results) - 5} more items")

        # Lead time
        lead_time = result.get('estimated_lead_time', 0)
        print(f"\n{self.CYAN}Estimated Lead Time:{self.END} {lead_time} days")

    def run_stage_5_pricing(self) -> bool:
        """Run Stage 5: Pricing Assessment with user interaction"""
        self.print_stage(5, "Pricing Assessment")

        print("This stage will calculate dynamic pricing and apply contract terms.")
        print("Base pricing, discounts, and contract conditions will be evaluated.")

        if not self.get_user_approval("Run pricing assessment?"):
            return False

        try:
            # Initialize Pricing Management Agent
            pricing_agent = PricingManagementAgent()

            print(f"\n{self.BLUE}Calculating dynamic pricing...{self.END}")

            # Get order data from previous stages
            parsed_data = self.workflow_data.get('parsed_data', {})
            credit_result = self.stage_results.get('credit', {})
            inventory_result = self.stage_results.get('inventory', {})

            # Run pricing assessment
            result = pricing_agent.calculate_pricing(parsed_data, credit_result, inventory_result)

            if result:
                self.display_pricing_results(result)

                # Store result for next stage
                self.stage_results['pricing'] = result

                # Check if pricing assessment passed
                pricing_status = result.get('status', 'UNKNOWN')
                if pricing_status in ['APPROVED', 'APPROVED_WITH_CONDITIONS']:
                    self.print_success(f"Pricing assessment completed: {pricing_status}")
                    return True
                else:
                    self.print_error(f"Pricing assessment failed: {pricing_status}")
                    return False
            else:
                self.print_error("Pricing assessment failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Pricing assessment error: {str(e)}")
            return False

    def display_pricing_results(self, result: Dict):
        """Display pricing assessment results"""
        print(f"\n{self.GREEN}{self.BOLD}Pricing Assessment Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Pricing status
        status = result.get('status', 'UNKNOWN')
        status_color = self.GREEN if status.startswith('APPROVED') else self.RED
        print(f"{self.CYAN}Pricing Status:{self.END} {status_color}{status}{self.END}")

        # Final pricing
        final_pricing = result.get('final_pricing', {})
        subtotal = final_pricing.get('subtotal', 0)
        discount = final_pricing.get('total_discount', 0)
        tax = final_pricing.get('tax_amount', 0)
        total = final_pricing.get('final_total', 0)

        print(f"{self.CYAN}Subtotal:{self.END} ${subtotal:,.2f}")
        if discount > 0:
            print(f"{self.CYAN}Discount:{self.END} -${discount:,.2f}")
        print(f"{self.CYAN}Tax:{self.END} ${tax:,.2f}")
        print(f"{self.CYAN}Final Total:{self.END} ${total:,.2f}")

        # Contract terms
        contract_terms = result.get('contract_terms', {})
        if contract_terms:
            print(f"\n{self.CYAN}Applied Contract Terms:{self.END}")
            discount_rate = contract_terms.get('discount_rate', 0)
            payment_terms = contract_terms.get('payment_terms', 'N/A')
            print(f"  • Discount Rate: {discount_rate}%")
            print(f"  • Payment Terms: {payment_terms}")

    def run_stage_6_fulfillment(self) -> bool:
        """Run Stage 6: Fulfillment Coordination with user interaction"""
        self.print_stage(6, "Fulfillment Coordination")

        print("This stage will coordinate warehouse operations and shipping.")
        print("Pick lists, shipping carriers, and delivery tracking will be set up.")

        if not self.get_user_approval("Run fulfillment coordination?"):
            return False

        try:
            # Initialize Fulfillment Coordination Agent
            fulfillment_agent = FulfillmentCoordinationAgent()

            print(f"\n{self.BLUE}Coordinating fulfillment and shipping...{self.END}")

            # Get order data from previous stages
            order_data = {
                'parsed_data': self.workflow_data.get('parsed_data', {}),
                'validation': self.stage_results.get('validation', {}),
                'credit': self.stage_results.get('credit', {}),
                'inventory': self.stage_results.get('inventory', {}),
                'pricing': self.stage_results.get('pricing', {})
            }

            # Run fulfillment coordination
            result = fulfillment_agent.coordinate_fulfillment(order_data)

            if result:
                self.display_fulfillment_results(result)

                # Store result for next stage
                self.stage_results['fulfillment'] = result

                # Check if fulfillment coordination passed
                fulfillment_status = result.get('fulfillment_status', 'UNKNOWN')
                if fulfillment_status in ['COORDINATED', 'READY_FOR_SHIPMENT']:
                    self.print_success(f"Fulfillment coordination completed: {fulfillment_status}")
                    return True
                else:
                    self.print_error(f"Fulfillment coordination failed: {fulfillment_status}")
                    return False
            else:
                self.print_error("Fulfillment coordination failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Fulfillment coordination error: {str(e)}")
            return False

    def display_fulfillment_results(self, result: Dict):
        """Display fulfillment coordination results"""
        print(f"\n{self.GREEN}{self.BOLD}Fulfillment Coordination Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Fulfillment status
        fulfillment_status = result.get('fulfillment_status', 'UNKNOWN')
        print(f"{self.CYAN}Fulfillment Status:{self.END} {self.GREEN}{fulfillment_status}{self.END}")

        # Warehouse details
        warehouse_info = result.get('warehouse_optimization', {})
        selected_warehouse = warehouse_info.get('selected_warehouse', 'N/A')
        print(f"{self.CYAN}Selected Warehouse:{self.END} {selected_warehouse}")

        # Shipping details
        shipping_info = result.get('shipping_coordination', {})
        carrier_info = shipping_info.get('selected_carrier', {})
        carrier = carrier_info.get('carrier', 'N/A')
        service = carrier_info.get('service', 'N/A')
        cost = carrier_info.get('cost', 0)
        delivery_date = carrier_info.get('delivery_date', 'N/A')

        print(f"{self.CYAN}Shipping Carrier:{self.END} {carrier} ({service})")
        print(f"{self.CYAN}Shipping Cost:{self.END} ${cost:.2f}")
        print(f"{self.CYAN}Estimated Delivery:{self.END} {delivery_date}")

        # Tracking info
        tracking_info = result.get('delivery_tracking', {})
        tracking_number = tracking_info.get('tracking_number', 'N/A')
        if tracking_number != 'N/A':
            print(f"{self.CYAN}Tracking Number:{self.END} {tracking_number}")

    def run_stage_7_invoice(self) -> bool:
        """Run Stage 7: Invoice Generation with user interaction"""
        self.print_stage(7, "Invoice Generation & Email Distribution")

        print("This stage will generate a professional invoice and email it to the customer.")
        print("Payment terms, contact information, and delivery confirmation will be included.")

        if not self.get_user_approval("Generate and send invoice?"):
            return False

        try:
            # Initialize Invoice Generation Agent
            invoice_agent = InvoiceGenerationAgent()

            print(f"\n{self.BLUE}Generating invoice and sending email...{self.END}")

            # Get complete order data
            order_data = {
                'parsed_data': self.workflow_data.get('parsed_data', {}),
                'validation': self.stage_results.get('validation', {}),
                'credit': self.stage_results.get('credit', {}),
                'inventory': self.stage_results.get('inventory', {}),
                'pricing': self.stage_results.get('pricing', {}),
                'fulfillment': self.stage_results.get('fulfillment', {})
            }

            # Run invoice generation
            result = invoice_agent.generate_and_send_invoice(order_data)

            if result:
                self.display_invoice_results(result)

                # Store result
                self.stage_results['invoice'] = result

                # Check if invoice generation passed
                invoice_status = result.get('status', 'UNKNOWN')
                if invoice_status == 'SUCCESS':
                    self.print_success("Invoice generated and sent successfully!")
                    return True
                else:
                    self.print_error(f"Invoice generation failed: {invoice_status}")
                    return False
            else:
                self.print_error("Invoice generation failed: No result returned")
                return False

        except Exception as e:
            self.print_error(f"Invoice generation error: {str(e)}")
            return False

    def display_invoice_results(self, result: Dict):
        """Display invoice generation results"""
        print(f"\n{self.GREEN}{self.BOLD}Invoice Generation Results:{self.END}")
        print(f"{self.GREEN}{'─'*50}{self.END}")

        # Invoice details
        invoice_number = result.get('invoice_number', 'N/A')
        invoice_date = result.get('invoice_date', 'N/A')
        due_date = result.get('due_date', 'N/A')
        total_amount = result.get('total_amount', 0)

        print(f"{self.CYAN}Invoice Number:{self.END} {invoice_number}")
        print(f"{self.CYAN}Invoice Date:{self.END} {invoice_date}")
        print(f"{self.CYAN}Due Date:{self.END} {due_date}")
        print(f"{self.CYAN}Total Amount:{self.END} ${total_amount:,.2f}")

        # Email delivery
        recipient = result.get('recipient', 'N/A')
        sent_at = result.get('sent_at', 'N/A')

        print(f"\n{self.CYAN}Email Delivery:{self.END}")
        print(f"  • Recipient: {recipient}")
        print(f"  • Sent At: {sent_at}")

        # File location
        invoice_path = result.get('invoice_path', 'N/A')
        print(f"\n{self.CYAN}Invoice File:{self.END} {invoice_path}")

    def display_workflow_summary(self):
        """Display complete workflow summary"""
        self.print_header("WORKFLOW COMPLETION SUMMARY")

        # Overall status
        print(f"{self.GREEN}{self.BOLD}Workflow Status: COMPLETED SUCCESSFULLY{self.END}")
        print(f"{self.GREEN}All 7 stages executed successfully!{self.END}\n")

        # Stage summary
        stages = [
            ("Email Monitoring", "New PO detected and processed"),
            ("PO Parsing", "Structured data extracted successfully"),
            ("Order Validation", "Comprehensive validation completed"),
            ("Credit Assessment", "Customer credit approved"),
            ("Inventory Assessment", "Stock availability confirmed"),
            ("Pricing Assessment", "Dynamic pricing calculated"),
            ("Fulfillment Coordination", "Shipping and warehouse coordinated"),
            ("Invoice Generation", "Invoice generated and emailed")
        ]

        print(f"{self.BLUE}{self.BOLD}Stage Summary:{self.END}")
        print(f"{self.BLUE}{'─'*60}{self.END}")

        for i, (stage_name, description) in enumerate(stages, 1):
            print(f"{self.GREEN}✓{self.END} Stage {i}: {self.CYAN}{stage_name}{self.END}")
            print(f"   {description}")

        # Key metrics
        parsed_data = self.workflow_data.get('parsed_data', {})
        po_number = parsed_data.get('po_number', 'N/A')
        customer_name = parsed_data.get('customer', {}).get('name', 'N/A')
        total_amount = parsed_data.get('pricing', {}).get('total', 0)

        print(f"\n{self.BLUE}{self.BOLD}Key Metrics:{self.END}")
        print(f"{self.BLUE}{'─'*30}{self.END}")
        print(f"{self.CYAN}PO Number:{self.END} {po_number}")
        print(f"{self.CYAN}Customer:{self.END} {customer_name}")
        print(f"{self.CYAN}Order Value:{self.END} ${total_amount:,.2f}")

        # Next steps
        print(f"\n{self.BLUE}{self.BOLD}Next Steps:{self.END}")
        print(f"{self.BLUE}{'─'*20}{self.END}")
        print("• Monitor delivery tracking")
        print("• Follow up on payment")
        print("• Review workflow metrics")
        print("• Archive completed order")

    def run_complete_workflow(self):
        """Run the complete O2C workflow with user interaction"""
        self.print_header("O2C INTERACTIVE WORKFLOW")

        print("Welcome to the O2C Interactive CLI!")
        print("This interface will guide you through each stage of the Order-to-Cash workflow.")
        print("You'll have control over each step and can see detailed results.")

        if not self.get_user_approval("Start the complete O2C workflow?"):
            print(f"\n{self.YELLOW}Workflow cancelled by user.{self.END}")
            return

        # Stage 0: Email Monitoring
        po_filename = self.run_email_monitoring()
        if not po_filename:
            print(f"\n{self.RED}Workflow terminated: No PO file to process.{self.END}")
            return

        # Stage 1: PO Parsing
        if not self.run_stage_1_parsing(po_filename):
            print(f"\n{self.RED}Workflow terminated: PO parsing failed.{self.END}")
            return

        # Stage 2: Order Validation
        if not self.run_stage_2_validation():
            print(f"\n{self.RED}Workflow terminated: Order validation failed.{self.END}")
            return

        # Stage 3: Credit Assessment
        if not self.run_stage_3_credit():
            print(f"\n{self.RED}Workflow terminated: Credit assessment failed.{self.END}")
            return

        # Stage 4: Inventory Assessment
        if not self.run_stage_4_inventory():
            print(f"\n{self.RED}Workflow terminated: Inventory assessment failed.{self.END}")
            return

        # Stage 5: Pricing Assessment
        if not self.run_stage_5_pricing():
            print(f"\n{self.RED}Workflow terminated: Pricing assessment failed.{self.END}")
            return

        # Stage 6: Fulfillment Coordination
        if not self.run_stage_6_fulfillment():
            print(f"\n{self.RED}Workflow terminated: Fulfillment coordination failed.{self.END}")
            return

        # Stage 7: Invoice Generation
        if not self.run_stage_7_invoice():
            print(f"\n{self.RED}Workflow terminated: Invoice generation failed.{self.END}")
            return

        # Workflow completed successfully
        self.display_workflow_summary()


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="O2C Interactive CLI - Step-by-step Order-to-Cash workflow",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 o2c_cli.py                    # Run complete interactive workflow
  python3 o2c_cli.py --help             # Show this help message
        """
    )

    args = parser.parse_args()

    # Initialize and run CLI
    cli = O2CCLI()

    try:
        cli.run_complete_workflow()
    except KeyboardInterrupt:
        print(f"\n\n{cli.RED}Workflow interrupted by user (Ctrl+C).{cli.END}")
        print(f"{cli.YELLOW}Partial results may be available in the data directories.{cli.END}")
    except Exception as e:
        print(f"\n\n{cli.RED}Unexpected error: {str(e)}{cli.END}")
        print(f"{cli.YELLOW}Please check the logs for more details.{cli.END}")


if __name__ == "__main__":
    main()
