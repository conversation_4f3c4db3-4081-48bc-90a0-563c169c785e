# Complete O2C (Order-to-Cash) Agentic Workflow Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Email Monitoring System](#email-monitoring-system)
3. [Complete O2C Workflow](#complete-o2c-workflow)
4. [Agent Details](#agent-details)
5. [Workflow Orchestration](#workflow-orchestration)
6. [Commands & Usage](#commands--usage)
7. [File Structure](#file-structure)
8. [Configuration](#configuration)

---

## System Overview

The O2C Agentic System is a complete **7-stage automated workflow** that processes purchase orders from email receipt to invoice delivery. Built using **CrewAI framework** with **DeepSeek LLM**, it provides end-to-end business process automation.

### **Key Features:**
- **Automated Email Monitoring** - Monitors Gmail for PO emails
- **7 Specialized AI Agents** - Each handling specific business functions
- **Intelligent Workflow Orchestration** - Manages agent coordination
- **SQLite Master Data Integration** - Customer, product, and pricing data
- **Professional Invoice Generation** - TXT format with email distribution
- **Complete Automation** - From email to invoice without human intervention

---

## Email Monitoring System

### **How Email Monitoring Works:**

The email monitoring system continuously watches your Gmail inbox for purchase order emails and automatically triggers the O2C workflow.

#### **Configuration:**
- **Gmail Account:** [Configured in email_config.json]
- **App Password:** [Configured in email_config.json]
- **Detection Method:** Subject keywords, sender domains, and body content analysis
- **Polling Interval:** 30 seconds (configurable)

#### **Detection Criteria:**
- **Subject Keywords:** "Purchase Order", "PO", "Order"
- **Sender Domains:** Trusted supplier domains
- **Body Keywords:** PO numbers, order references
- **Attachment Types:** Text format PO attachments

#### **Processing Flow:**
1. **Connect to Gmail** using IMAP
2. **Scan for new emails** matching PO criteria
3. **Extract PO content** from email body and attachments
4. **Save PO files** to `data/incoming_pos/` folder
5. **Trigger O2C workflow** automatically
6. **Mark emails as processed** to avoid reprocessing

### **Commands to Start Email Monitoring:**

#### **Main Command (Continuous Monitoring):**
```bash
python3 run_email_monitor.py --run
```

#### **Other Commands:**
```bash
# Check current status
python3 run_email_monitor.py --status

# Run single email check (test)
python3 run_email_monitor.py --check

# Setup Gmail configuration
python3 run_email_monitor.py --setup

# Run tests
python3 run_email_monitor.py --test
```

---

## 🔄 Complete O2C Workflow

### **7-Stage Workflow Overview:**

```
Email → Parse → Validate → Credit → Inventory → Pricing → Fulfillment → Invoice
```

### **Detailed Stage Breakdown:**

| Stage | Agent | Purpose | Input | Output |
|-------|-------|---------|-------|--------|
| 1 | **PO Parser** | Extract structured data from PO | Raw PO file | Structured JSON |
| 2 | **Order Validation** | Comprehensive order validation | Parsed JSON | Validation report |
| 3 | **Credit Assessment** | Customer credit and risk analysis | Customer data | Credit decision |
| 4 | **Inventory Assessment** | Stock availability check | Product codes | Fulfillment plan |
| 5 | **Pricing Assessment** | Dynamic pricing and contracts | Order details | Final pricing |
| 6 | **Fulfillment Coordination** | Warehouse and shipping coordination | Approved order | Shipping details |
| 7 | **Invoice Generation** | Create and email invoice | Completed order | Invoice sent |

### **Workflow States:**
- `PENDING` - Workflow initiated
- `PARSING` - PO being parsed
- `VALIDATING` - Order validation in progress
- `CREDIT_REVIEW` - Credit assessment
- `INVENTORY_CHECK` - Stock verification
- `PRICING_CALCULATION` - Price determination
- `FULFILLMENT_COORDINATION` - Shipping coordination
- `INVOICE_GENERATION` - Invoice creation
- `INVOICE_SENT` - Complete (final state)

---

## Agent Details

### **1. PO Parser Agent**
- **Role:** Senior Purchase Order Parser
- **Purpose:** Extract structured data from purchase order files
- **Tools:**
  - `po_file_reader` - Read PO files from incoming folder
  - `po_data_saver` - Save structured JSON data
  - `po_validator` - Validate extracted data
- **Input:** Raw PO file (TXT format)
- **Output:** Structured JSON with customer, line items, pricing, delivery info
- **Key Features:**
  - Handles email metadata, body, and text attachments
  - Extracts vendor, customer, line items, pricing, delivery, terms
  - Converts to standardized JSON format

### **2. Order Validation Agent**
- **Role:** Senior Order Validation Specialist  
- **Purpose:** Comprehensive validation of parsed order data
- **Tools:**
  - `parsed_order_reader` - Read parsed order JSON
  - `customer_validator` - Validate customer existence and status
  - `product_catalog_validator` - Verify product codes and pricing
  - `business_rules_validator` - Apply business rule validations
  - `delivery_address_validator` - Validate delivery information
  - `compliance_validator` - Regulatory compliance checks
  - `risk_assessment_calculator` - Calculate overall risk score
- **Input:** Parsed order JSON
- **Output:** Comprehensive validation report with PASS/REVIEW/FAIL status
- **Key Features:**
  - 7-layer validation process
  - Risk scoring and recommendations
  - Compliance and regulatory checks

### **3. Credit Management Agent**
- **Role:** Senior Credit Risk Analyst
- **Purpose:** Assess customer creditworthiness and payment risk
- **Tools:**
  - `customer_credit_checker` - Check credit limits and history
  - `risk_calculator` - Calculate credit risk scores
  - `credit_decision_maker` - Make approval decisions
- **Input:** Customer information and order total
- **Output:** Credit decision (APPROVE/REVIEW/DECLINE) with conditions
- **Key Features:**
  - Credit limit verification
  - Payment history analysis
  - Risk-based decision making

### **4. Inventory Management Agent**
- **Role:** Senior Inventory Specialist
- **Purpose:** Verify product availability and plan fulfillment
- **Tools:**
  - `inventory_checker` - Check stock levels
  - `fulfillment_planner` - Plan order fulfillment
  - `supplier_coordinator` - Coordinate with suppliers if needed
- **Input:** Validated order with line items
- **Output:** Fulfillment plan with availability status
- **Key Features:**
  - Real-time inventory checking
  - Multi-location inventory management
  - Supplier coordination for out-of-stock items

### **5. Pricing Management Agent**
- **Role:** Senior Pricing Analyst
- **Purpose:** Calculate dynamic pricing and apply contract terms
- **Tools:**
  - `pricing_calculator` - Calculate base and dynamic pricing
  - `contract_manager` - Apply contract terms and discounts
  - `approval_manager` - Handle pricing approvals
- **Input:** Validated order with inventory confirmation
- **Output:** Final pricing with contract terms applied
- **Key Features:**
  - Dynamic pricing based on volume, customer tier, market conditions
  - Contract-based pricing and discount application
  - Automated approval workflows for pricing exceptions

### **6. Fulfillment Coordination Agent**
- **Role:** Senior Fulfillment Coordination Specialist
- **Purpose:** Coordinate warehouse operations and shipping
- **Tools:**
  - `warehouse_optimizer` - Optimize warehouse allocation
  - `pick_list_generator` - Generate pick lists and work orders
  - `shipping_carrier_selector` - Select optimal shipping carrier
  - `shipping_label_creator` - Create shipping labels
  - `delivery_tracker` - Track delivery status
  - `fulfillment_exception_handler` - Handle exceptions
- **Input:** Approved order with final pricing
- **Output:** Complete fulfillment coordination with shipping details
- **Key Features:**
  - Warehouse optimization and allocation
  - Pick list generation and work order management
  - Carrier selection and shipping coordination
  - Real-time delivery tracking

### **7. Invoice Generation & Email Distribution Agent**
- **Role:** Senior Invoice Generation & Distribution Specialist
- **Purpose:** Generate professional invoices and email to customers
- **Tools:**
  - `invoice_generator` - Generate TXT format invoices
  - `invoice_email_sender` - Send invoices via email
- **Input:** Completed order with fulfillment details
- **Output:** Professional invoice emailed to customer
- **Key Features:**
  - Professional TXT format invoice generation
  - Automated email distribution
  - Payment terms and contact information inclusion
  - Delivery confirmation tracking

---

## Workflow Orchestration

### **Workflow Orchestrator (`services/workflow_orchestrator.py`)**

The workflow orchestrator manages the complete O2C process, coordinating between agents and maintaining workflow state.

#### **Key Functions:**
- **Agent Coordination:** Manages sequential execution of all 7 agents
- **State Management:** Tracks workflow progress and status
- **Error Handling:** Manages failures and retry logic
- **Data Flow:** Ensures proper data passing between agents
- **Logging:** Comprehensive logging of all workflow activities

#### **Workflow Execution Flow:**
1. **Initialize Workflow** - Create workflow ID and setup logging
2. **Stage 1: PO Parsing** - Extract structured data from PO
3. **Stage 2: Order Validation** - Comprehensive validation checks
4. **Stage 3: Credit Assessment** - Customer credit and risk analysis
5. **Stage 4: Inventory Assessment** - Stock availability verification
6. **Stage 5: Pricing Assessment** - Dynamic pricing calculation
7. **Stage 6: Fulfillment Coordination** - Warehouse and shipping coordination
8. **Stage 7: Invoice Generation** - Create and email invoice
9. **Complete Workflow** - Save results and update status

#### **Workflow Results:**
All workflow results are saved to `data/workflow_results/workflow_[timestamp].json` containing:
- Complete workflow execution details
- Each stage's input, output, and status
- Timing information and performance metrics
- Error details if any stage fails
- Final workflow status and recommendations

---

## Commands & Usage

### **Email Monitoring Commands:**

#### **Start Continuous Monitoring:**
```bash
python3 run_email_monitor.py --run
```
- Monitors Gmail continuously for new PO emails
- Automatically triggers O2C workflow when POs detected
- Press Ctrl+C to stop

#### **Check System Status:**
```bash
python3 run_email_monitor.py --status
```
- Shows current configuration
- Displays processed PO count
- Checks log file status

#### **Run Single Email Check:**
```bash
python3 run_email_monitor.py --check
```
- Performs one-time email check
- Useful for testing and debugging

#### **Setup Gmail Configuration:**
```bash
python3 run_email_monitor.py --setup
```
- Interactive Gmail setup
- Configures email credentials and detection settings

### **Manual Workflow Testing:**

#### **Test Complete O2C Workflow:**
```bash
python3 test_pricing_workflow.py
```
- Creates test PO and runs complete 7-stage workflow
- Useful for testing and validation

#### **Test Individual Components:**
```bash
# Test credit management
python3 test_credit_agent.py

# Test inventory integration
python3 test_inventory_integration.py

# Test SQLite integration
python3 test_sqlite_integration.py
```

### **Database Setup:**
```bash
# Setup master data database
python3 setup_master_data.py

# Setup database schema
python3 database/setup_database.py
```

---

## File Structure

```
EM O2C/
├── Email Monitoring
│   ├── run_email_monitor.py          # Main email monitoring runner
│   ├── services/email_monitor.py     # Email monitoring service
│   └── config/email_config.json      # Email configuration
│
├── Agents
│   ├── agents/po_parser_agent.py           # Stage 1: PO Parsing
│   ├── agents/order_validation_agent.py    # Stage 2: Order Validation
│   ├── agents/credit_management_agent.py   # Stage 3: Credit Assessment
│   ├── agents/inventory_management_agent.py # Stage 4: Inventory Assessment
│   ├── agents/pricing_management_agent.py  # Stage 5: Pricing Assessment
│   ├── agents/fulfillment_coordination_agent.py # Stage 6: Fulfillment
│   ├── agents/invoice_generation_agent.py  # Stage 7: Invoice Generation
│   └── agents/tools/                       # Agent tools and utilities
│
├── Workflow Orchestration
│   ├── services/workflow_orchestrator.py   # Main workflow coordinator
│   └── o2c_crew.py                         # CrewAI crew configuration
│
├── Data Storage
│   ├── data/incoming_pos/             # Incoming PO files
│   ├── data/parsed_pos/               # Parsed PO JSON files
│   ├── data/processed_pos/            # Processed PO archive
│   ├── data/invoices/                 # Generated invoices
│   ├── data/workflow_results/         # Workflow execution results
│   └── data/processed_emails/         # Email processing logs
│
├── Database
│   ├── database/o2c_master_data.db    # SQLite master data
│   ├── database/schema.sql            # Database schema
│   └── database/setup_database.py     # Database setup script
│
├── Configuration
│   ├── config/email_config.json       # Email monitoring config
│   ├── config/deepseek_config.json    # LLM configuration
│   └── config/llm_config.py           # LLM setup
│
├── Testing
│   ├── test_pricing_workflow.py       # Complete workflow test
│   ├── tests/test_email_monitor.py    # Email monitoring tests
│   └── test_scenarios/                # Various test scenarios
│
└── Documentation
    ├── O2C_WORKFLOW_DOCUMENTATION.md  # This documentation
    ├── README.md                      # Project overview
    └── *.md                          # Various documentation files
```

---

## Configuration

### **Email Configuration (`config/email_config.json`):**
```json
{
  "gmail": {
    "username": "[YOUR_GMAIL_ADDRESS]",
    "app_password": "[YOUR_APP_PASSWORD]",
    "imap_server": "imap.gmail.com",
    "imap_port": 993
  },
  "po_detection": {
    "subject_keywords": ["Purchase Order", "PO", "Order"],
    "sender_domains": ["abcmfg.com", "supplier.com"],
    "body_keywords": ["PO-", "Purchase Order", "Order Number"]
  },
  "processing": {
    "polling_interval": 30,
    "max_emails_per_check": 10,
    "output_directory": "data/incoming_pos"
  }
}
```

### **DeepSeek LLM Configuration:**
- **Model:** deepseek/deepseek-chat
- **Temperature:** 0.4
- **API Key:** [Configured in deepseek_config.json]
- **Framework:** CrewAI with LangChain integration

### **Database Configuration:**
- **Type:** SQLite
- **File:** database/o2c_master_data.db
- **Tables:** customers, products, pricing, inventory, contracts

---

## Final Workflow Output

### **Successful Workflow Completion:**

When the complete O2C workflow executes successfully, the final output includes:

#### **1. Workflow Status: `INVOICE_SENT`**
- All 7 stages completed successfully
- Invoice generated and emailed to customer
- Complete audit trail maintained

#### **2. Generated Files:**
- **Parsed PO:** `data/parsed_pos/PARSED_[PO_NUMBER].json`
- **Invoice:** `data/invoices/INVOICE_[INVOICE_NUMBER].txt`
- **Workflow Results:** `data/workflow_results/workflow_[TIMESTAMP].json`

#### **3. Email Deliverables:**
- Professional invoice emailed to customer
- Payment terms and contact information included
- Delivery confirmation received

#### **4. Business Outcomes:**
- Order processed from receipt to invoice automatically
- Customer credit verified and approved
- Inventory allocated and fulfillment coordinated
- Dynamic pricing applied with contract terms
- Professional invoice delivered to customer
- Complete audit trail for compliance

### **Example Final Status:**
```
Workflow workflow_20250718_112612 completed with status: INVOICE_SENT
Total Execution Time: ~1.5 hours
Order Value: $21,700.00
Invoice Sent: [CUSTOMER_EMAIL]
All 7 Stages: SUCCESS
```

---

## Getting Started

### **Quick Start:**
1. **Start Email Monitoring:**
   ```bash
   python3 run_email_monitor.py --run
   ```

2. **Send Test PO Email** to your configured Gmail address

3. **Monitor Logs** for automatic workflow execution

4. **Check Results** in `data/workflow_results/`

### **The system will automatically:**
- Detect your PO email
- Extract and parse PO data
- Validate order and customer
- Assess credit and inventory
- Calculate dynamic pricing
- Coordinate fulfillment
- Generate and email invoice

**Complete automation from email to invoice!**
