#!/usr/bin/env python3
"""
Verbosity Management for O2C Agents
Centralized configuration for controlling agent verbosity and logging levels
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional

class VerbosityManager:
    """Manages verbosity settings for all O2C agents and workflows"""

    def __init__(self, config_path: str = "config/verbosity_config.json"):
        """Initialize verbosity manager with configuration file"""
        self.config_path = config_path
        self.config = self.load_config()
        self.setup_logging()

    def load_config(self) -> Dict[str, Any]:
        """Load verbosity configuration from JSON file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    return json.load(f)
            else:
                # Return default configuration if file doesn't exist
                return self.get_default_config()
        except Exception as e:
            print(f"Warning: Failed to load verbosity config: {e}")
            return self.get_default_config()

    def get_default_config(self) -> Dict[str, Any]:
        """Return default verbosity configuration"""
        return {
            "verbosity_settings": {
                "global_verbose": False,
                "agent_verbose": False,
                "crew_verbose": False,
                "task_verbose": False,
                "tool_verbose": False,
                "workflow_verbose": False
            },
            "individual_agent_settings": {},
            "logging_levels": {
                "crewai_logging": "WARNING",
                "agent_logging": "WARNING",
                "tool_logging": "WARNING",
                "workflow_logging": "INFO",
                "litellm_logging": "WARNING"
            },
            "output_control": {
                "show_agent_thoughts": False,
                "show_tool_inputs": False,
                "show_tool_outputs": False,
                "show_task_progress": True,
                "show_final_results": True,
                "show_execution_time": True
            },
            "debug_mode": {
                "enabled": False,
                "detailed_errors": False,
                "trace_execution": False,
                "save_debug_logs": False
            }
        }

    def setup_logging(self):
        """Configure logging levels based on verbosity settings"""
        logging_levels = self.config.get("logging_levels", {})

        # Set logging levels for different components
        loggers = {
            "crewai": logging_levels.get("crewai_logging", "WARNING"),
            "agents": logging_levels.get("agent_logging", "WARNING"),
            "tools": logging_levels.get("tool_logging", "WARNING"),
            "workflow": logging_levels.get("workflow_logging", "INFO"),
            "litellm": logging_levels.get("litellm_logging", "ERROR"),
            "LiteLLM": logging_levels.get("litellm_logging", "ERROR"),
            "httpx": logging_levels.get("httpx_logging", "ERROR"),
            "opentelemetry": logging_levels.get("opentelemetry_logging", "ERROR"),
            "opentelemetry.trace": logging_levels.get("opentelemetry_logging", "ERROR")
        }

        for logger_name, level in loggers.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(getattr(logging, level.upper(), logging.WARNING))

    def get_agent_verbose(self, agent_name: str) -> bool:
        """Get verbose setting for a specific agent"""
        # Check global settings first - they override everything
        verbosity_settings = self.config.get("verbosity_settings", {})
        if verbosity_settings.get("global_verbose", False):
            return True

        # Check individual agent settings
        individual_settings = self.config.get("individual_agent_settings", {})
        if agent_name in individual_settings:
            return individual_settings[agent_name].get("verbose", False)

        # Fall back to general agent verbose setting
        return verbosity_settings.get("agent_verbose", False)

    def get_crew_verbose(self) -> bool:
        """Get verbose setting for crew execution"""
        verbosity_settings = self.config.get("verbosity_settings", {})
        if verbosity_settings.get("global_verbose", False):
            return True
        return verbosity_settings.get("crew_verbose", False)

    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get complete configuration for a specific agent"""
        individual_settings = self.config.get("individual_agent_settings", {})
        agent_config = individual_settings.get(agent_name, {})

        # Ensure verbose setting is included
        if "verbose" not in agent_config:
            agent_config["verbose"] = self.get_agent_verbose(agent_name)

        return agent_config

    def set_global_verbose(self, verbose: bool):
        """Set global verbosity for all components"""
        self.config["verbosity_settings"]["global_verbose"] = verbose
        self.save_config()
        self.setup_logging()

    def set_agent_verbose(self, agent_name: str, verbose: bool):
        """Set verbosity for a specific agent"""
        if "individual_agent_settings" not in self.config:
            self.config["individual_agent_settings"] = {}

        if agent_name not in self.config["individual_agent_settings"]:
            self.config["individual_agent_settings"][agent_name] = {}

        self.config["individual_agent_settings"][agent_name]["verbose"] = verbose
        self.save_config()

    def disable_all_verbosity(self):
        """Disable verbosity for all components"""
        self.config["verbosity_settings"] = {
            "global_verbose": False,
            "agent_verbose": False,
            "crew_verbose": False,
            "task_verbose": False,
            "tool_verbose": False,
            "workflow_verbose": False
        }

        # Disable individual agent verbosity
        for agent_name in self.config.get("individual_agent_settings", {}):
            self.config["individual_agent_settings"][agent_name]["verbose"] = False

        self.save_config()
        self.setup_logging()

    def save_config(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Warning: Failed to save verbosity config: {e}")

    def print_current_settings(self):
        """Print current verbosity settings"""
        print("=" * 60)
        print("CURRENT VERBOSITY SETTINGS")
        print("=" * 60)

        verbosity_settings = self.config.get("verbosity_settings", {})
        print(f"Global Verbose: {verbosity_settings.get('global_verbose', False)}")
        print(f"Agent Verbose: {verbosity_settings.get('agent_verbose', False)}")
        print(f"Crew Verbose: {verbosity_settings.get('crew_verbose', False)}")

        print("\nIndividual Agent Settings:")
        individual_settings = self.config.get("individual_agent_settings", {})
        for agent_name, settings in individual_settings.items():
            print(f"  {agent_name}: verbose={settings.get('verbose', False)}")


# Global verbosity manager instance
verbosity_manager = VerbosityManager()


def get_agent_verbose(agent_name: str) -> bool:
    """Convenience function to get agent verbosity setting"""
    return verbosity_manager.get_agent_verbose(agent_name)


def get_crew_verbose() -> bool:
    """Convenience function to get crew verbosity setting"""
    return verbosity_manager.get_crew_verbose()


def get_agent_config(agent_name: str) -> Dict[str, Any]:
    """Convenience function to get agent configuration"""
    return verbosity_manager.get_agent_config(agent_name)


def set_global_verbose(verbose: bool):
    """Convenience function to set global verbosity"""
    verbosity_manager.set_global_verbose(verbose)


def disable_all_verbosity():
    """Convenience function to disable all verbosity"""
    verbosity_manager.disable_all_verbosity()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Manage O2C Agent Verbosity Settings")
    parser.add_argument("--show", action="store_true", help="Show current settings")
    parser.add_argument("--global-verbose", type=bool, help="Set global verbosity (True/False)")
    parser.add_argument("--disable-all", action="store_true", help="Disable all verbosity")
    parser.add_argument("--agent", type=str, help="Agent name to configure")
    parser.add_argument("--agent-verbose", type=bool, help="Set agent verbosity (True/False)")

    args = parser.parse_args()

    if args.show:
        verbosity_manager.print_current_settings()

    if args.global_verbose is not None:
        verbosity_manager.set_global_verbose(args.global_verbose)
        print(f"Global verbosity set to: {args.global_verbose}")

    if args.disable_all:
        verbosity_manager.disable_all_verbosity()
        print("All verbosity disabled")

    if args.agent and args.agent_verbose is not None:
        verbosity_manager.set_agent_verbose(args.agent, args.agent_verbose)
        print(f"Agent {args.agent} verbosity set to: {args.agent_verbose}")

    if not any([args.show, args.global_verbose is not None, args.disable_all,
                (args.agent and args.agent_verbose is not None)]):
        verbosity_manager.print_current_settings()